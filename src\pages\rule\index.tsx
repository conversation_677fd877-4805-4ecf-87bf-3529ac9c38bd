import routerInstance from "@/common/router";
import image5 from "@assets/images/image5.jpg";
import { defineComponent, ref } from "vue";
import Description from "./components/description";
import Rule_content from "./components/rule_content";
import { getEndTime, getUserMobileCookie } from "@/common/cookies/user";
import { Modal } from "@arco-design/web-vue";

export default defineComponent(() => {
  // if (!getUserMobileCookie()) {
  //     console.log('zheli')
  //     routerInstance.replace({name: "index"});
  // }

  const toExam = () => {
    // routerInstance.push({ name: "exam" });
    const data = routerInstance.resolve({ name: "exam" });
    console.log(data);

    const { origin, pathname } = window.location;
    const url = origin + pathname + data.href;
    console.log(origin, pathname);

    window.location.href =
      window.location.origin + "/wp/tools/redirect?return_url=" + encodeURIComponent(url);
  };
  const components = [
    <Rule_content
      onNextEvent={() => {
        current.value += 1;
      }}
    />,
    <Description
      onBackEvent={() => {
        current.value -= 1;
      }}
      onNextEvent={() => {
        let endTime: any = getEndTime();
        const currentTime = Math.round(Date.now() / 1000);
        if (currentTime > endTime) {
          Modal.confirm({
            alignCenter: true,
            simple: true,
            hideCancel: true,
            width: "auto",
            top: "auto",
            content: () => (
              <div class="text-center">模考时间已结束，现在进入练习不计入排名统计</div>
            ),
            onOk() {
              toExam();
            },
          });
        } else {
          toExam();
        }
      }}
    />,
  ];

  const current = ref(0);
  return () => (
    <div
      class="w-screen h-screen "
      style={{
        backgroundImage: `url(${image5})`,
        backgroundSize: "100% 100%",
      }}
    >
      {components[current.value]}
    </div>
  );
});
