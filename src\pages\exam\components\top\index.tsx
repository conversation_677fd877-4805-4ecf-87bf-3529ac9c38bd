import { defineComponent } from "vue";
import Complete_count from "./components/complete_count";
import Font_size from "./components/font_size";
import Lock from "./components/lock";
import Name from "./components/name";
import Position from "./components/position";
import Time from "./components/time";
import EndTime from "./components/end_time";

export default defineComponent({
  setup() {
    return () => (
      <div class="bg-primary px-3 py-2 flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <Name />
        </div>
        <div class="flex items-center space-x-5">
          <Font_size />
          <Complete_count />
          <Time />
          <EndTime />
          <Lock />
          <Position />
        </div>
      </div>
    );
  },
});
