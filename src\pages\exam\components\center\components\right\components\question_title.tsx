import { useDataStore } from "@/pages/exam/store/dataStore";
import { defineComponent } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();
    return () => (
      <div class="flex space-x-3 items-start user-select-none">
        <div
          class="bg-primary text-white w-5 h-5 rounded flex justify-center items-center"
          style="margin-top:3px"
        >
          {dataStore.currentQuestion?.index}
        </div>
        <div
          class="flex-1 "
          style={{
            fontSize: dataStore.fontSize + "px",
          }}
          v-html={dataStore.currentQuestion?.title}
        ></div>
      </div>
    );
  },
});
