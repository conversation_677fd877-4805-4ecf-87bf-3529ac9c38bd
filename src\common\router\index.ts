import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";
import Index from "@pages/index";
import Rule from "@pages/rule";
import Exam from "@pages/exam";
import End from "@pages/end";
import Empty from "@pages/empty";
import selectd from "@pages/selectd";

// 配置路由映射
const routes: RouteRecordRaw[] = [
  {
    // 首页
    path: "/:examKey/",
    name: "index",
    component: Index,
  },
  {
    // 规则
    path: "/:examKey/rule",
    name: "rule",
    component: Rule,
  },
  {
    // 考试界面
    path: "/:examKey/exam",
    name: "exam",
    component: Exam,
  },
  {
    // 考试结束
    path: "/:examKey/end",
    name: "end",
    component: End,
  },
  {
    // 考试结束
    path: "/:examKey/end",
    name: "end",
    component: End,
  },
  {
    // 404 路由，用于处理未找到的路由
    path: "/:examKey/empty",
    name: "empty",
    component: Empty,
  },
  {
    path: "/:examKey/selectd",
    name: "selectd",
    component: selectd,
  },
];

const routerInstance = createRouter({
  history: createWebHashHistory(),
  routes,
});
routerInstance.beforeEach((to, from, next) => {
  if (to.matched.length === 0) {
    console.log(from);
    // 当没有匹配到任何路由时，重定向到 404 页面
    next({ name: "empty" });
  } else {
    next();
  }
});
// 导出路由，便于挂载
export default routerInstance;
