apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: ${INGRESS_CLASS}
  name: ${DEPLOYMENT_NAME}
  namespace: ${NAMESPACE}
spec:
  tls:
  - hosts:
    - ${DOMAIN}
    secretName: "${HTTPS_SECRET_NAME}"
  rules:
  - host: ${DOMAIN}
    http:
      paths:
      - backend:
          serviceName: ${DEPLOYMENT_NAME}
          servicePort: ${SERVICE_PORT}
        path: /${URI_PREFIX_S}
        
