import { useDataStore } from "@/pages/exam/store/dataStore";
import { Alert } from "@arco-design/web-vue";
import { computed, defineComponent } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    const describe = computed(() => {
      return dataStore.testPaperSource?.typer.find(
        (e) => e.id == dataStore.currentQuestion?.group_id
      );
    });

    return () => {
      return (
        <div class="space-y-3">
          <div class="font-bold user-select-none">{describe.value?.title ?? "--"}</div>
          {describe.value?.describe && (
            <Alert>
              <div
                class="user-select-none"
                style={{
                  fontSize: dataStore.fontSize + "px",
                }}
              >
                {describe.value?.describe || "--"}
              </div>
            </Alert>
          )}
        </div>
      );
    };
  },
});
