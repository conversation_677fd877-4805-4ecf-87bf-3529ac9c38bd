// import { mock } from "mockjs";

// mock(new RegExp("/Computer/getpaperinfo"), () => {
//   return {
//     status: 200,
//     msg: "S",
//     data: {
//       id: "3",
//       cs_id: "1",
//       c_id: "1",
//       qv_no: "qv5",
//       name: "《选调》2024年真题卷",
//       indexid: "1",
//       status: "1",
//       participants: "0",
//       typer: [
//         {
//           type: "1",
//           title: "\u4e00\u3001\u5355\u9879\u9009\u62e9\u9898",
//           describe:
//             "\u672c\u9898\u578b\u51715\u5c0f\u9898\uff0c\u6bcf\u5c0f\u989810\u5206\uff0c\u517150\u5206\uff0c\u6bcf\u5c0f\u9898\u53ea\u6709\u4e00\u4e2a\u6b63\u786e\u7b54\u6848\uff0c\u8bf7\u4ece\u6bcf\u5c0f\u9898\u7684\u5907\u9009\u7b54\u6848\u4e2d\u9009\u51fa\u4e00\u4e2a\u4f60\u8ba4\u4e3a\u6b63\u786e\u7684\u7b54\u6848\uff0c\u7528\u9f20\u6807\u70b9\u51fb\u76f8\u5e94\u7684\u9009\u9879\u3002",
//         },
//         {
//           type: "2",
//           title: "\u4e8c\u3001\u591a\u9879\u9009\u62e9\u9898",
//           describe:
//             "\u672c\u9898\u578b\u51712\u5c0f\u9898\uff0c\u6bcf\u5c0f\u98988\u5206\uff0c\u517116\u5206\uff0c\u6bcf\u5c0f\u9898\u5747\u6709\u591a\u4e2a\u6b63\u786e\u7b54\u6848\uff0c\u8bf7\u4ece\u6bcf\u5c0f\u9898\u7684\u5907\u9009\u7b54\u6848\u4e2d\u9009\u51fa\u4f60\u8ba4\u4e3a\u6b63\u786e\u7684\u7b54\u6848\uff0c\u7528\u9f20\u6807\u70b9\u51fb\u76f8\u5e94\u7684\u9009\u9879\u3002\u6bcf\u5c0f\u9898\u6240\u6709\u7b54\u6848\u9009\u62e9\u6b63\u786e\u7684\u5f97\u5206\uff0c\u4e0d\u7b54\u3001\u9519\u7b54\u3001\u6f0f\u7b54\u5747\u4e0d\u5f97\u5206\u3002",
//         },
//         {
//           type: "3",
//           title: "\u4e09\u3001\u5224\u65ad\u9898",
//           describe:
//             "\u672c\u9898\u578b\u51712\u5c0f\u9898\uff0c\u517118\u5206\uff0c\u5176\u4e2d\u4e00\u9053\u5c0f\u9898\u53ef\u4ee5\u9009\u7528\u4e2d\u6587\u6216\u82f1\u6587\u89e3\u7b54\uff0c\u8bf7\u4ed4\u7ec6\u9605\u8bfb\u7b54\u9898\u8981\u6c42\u3002\u5982\u4f7f\u7528\u82f1\u6587\u89e3\u7b54\uff0c\u987b\u5168\u90e8\u4f7f\u7528\u82f1\u6587\uff0c\u7b54\u9898\u6b63\u786e\u7684\uff0c\u589e\u52a05\u5206\u3002\u6d89\u53ca\u8ba1\u7b97\u7684\uff0c\u8981\u6c42\u5217\u51fa\u8ba1\u7b97\u6b65\u9aa4\uff0c\u5426\u5219\u4e0d\u5f97\u5206\uff0c\u9664\u975e\u9898\u76ee\u7279\u522b\u8bf4\u660e\u4e0d\u9700\u8981\u5217\u51fa\u8ba1\u7b97\u8fc7\u7a0b\u3002",
//         },
//         {
//           type: "10",
//           title: "\u56db\u3001\u8ba1\u7b97\u9898",
//           describe:
//             "\u672c\u9898\u578b\u51711\u5c0f\u9898\uff0c\u517116\u5206\uff0c\u6d89\u53ca\u8ba1\u7b97\u7684\uff0c\u8981\u6c42\u5217\u51fa\u8ba1\u7b97\u6b65\u9aa4\u3002",
//         },
//         {
//           type: "9",
//           title: "\u4e94\u3001\u6848\u4f8b\u5206\u6790\u9898",
//           describe:
//             "\u672c\u9898\u578b\u51711\u5c0f\u9898\uff0c\u517116\u5206\uff0c\u6d89\u53ca\u8ba1\u7b97\u7684\uff0c\u8981\u6c42\u5217\u51fa\u8ba1\u7b97\u6b65\u9aa4\u3002",
//         },
//       ],
//       test_time: "7200",
//       addtime: "1682751331",
//       updatetime: "1693807128",
//       question_total: 10,
//     },
//   };
// });

// mock(new RegExp("/Computer/getpaper"), () => {
//   return {
//     status: 200,
//     msg: "S",
//     data: {
//       no: "qv5",
//       id: "3",
//       prove: "1234",
//       exam: "\u6ce8\u518c\u4f1a\u8ba1\u5e08\u5168\u56fd\u7edf\u4e00\u8003\u8bd5",
//       subject: "选调",
//       questions: {
//         "1": {
//           type: "1",
//           title: "\u5355\u9879\u9009\u62e9\u9898",
//           list: [
//             {
//               id: 824,
//               s: "s3",
//               t: 1,
//               qt: 1,
//               qd: 0,
//               q: "\u5173\u4e8e\u8bbe\u7acb\u7ba1\u7406\u516c\u5f00\u52df\u96c6\u57fa\u91d1\u7684\u57fa\u91d1\u7ba1\u7406\u516c\u53f8\u6761\u4ef6\uff0c\u4ee5\u4e0b\u8868\u8ff0\u9519\u8bef\u7684\u662f\uff08 \uff09\u3002",
//               oa: {
//                 opt: [
//                   {
//                     k: "A",
//                     c: "\u4e3b\u8981\u80a1\u4e1c\u4e3a\u6cd5\u4eba\u7684\uff0c\u51c0\u8d44\u4ea7\u4e0d\u4f4e\u4e8e3000\u4e07\u5143\u4eba\u6c11\u5e01",
//                   },
//                   {
//                     k: "B",
//                     c: "\u4e3b\u8981\u80a1\u4e1c\u6301\u80a1\u6bd4\u4f8b\u572825%\u4ee5\u4e0a",
//                   },
//                   {
//                     k: "C",
//                     c: "\u4e3b\u8981\u80a1\u4e1c\u6700\u8fd13\u5e74\u6ca1\u6709\u8fdd\u6cd5\u8fdd\u89c4\u884c\u4e3a\u53d7\u5230\u884c\u653f\u5904\u7f5a",
//                   },
//                   {
//                     k: "D",
//                     c: "\u53d6\u5f97\u57fa\u91d1\u4ece\u4e1a\u8d44\u683c\u7684\u62df\u4efb\u9ad8\u7ea7\u7ba1\u7406\u4eba\u5458\u3001\u4e1a\u52a1\u4eba\u5458\u4e0d\u5c11\u4e8e15\u4eba",
//                   },
//                 ],
//                 a: ["A"],
//               },
//               ans: "\u4e3b\u8981\u80a1\u4e1c\u4e3a\u6cd5\u4eba\u51c0\u8d44\u4ea7\u4e0d\u4f4e\u4e8e2\u4ebf\u5143\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//               w: "",
//             },
//           ],
//         },
//         "2": {
//           type: "2",
//           title: "\u591a\u9879\u9009\u62e9\u9898",
//           list: [
//             {
//               id: 825,
//               s: "s3",
//               t: 2,
//               qt: 2,
//               qd: 0,
//               q: "\u5173\u4e8e\u8bbe\u7acb\u7ba1\u7406\u516c\u5f00\u52df\u96c6\u57fa\u91d1\u7684\u57fa\u91d1\u7ba1\u7406\u516c\u53f8\u6761\u4ef6\uff0c\u4ee5\u4e0b\u8868\u8ff0\u9519\u8bef\u7684\u662f\uff08 \uff09\u3002",
//               oa: {
//                 opt: [
//                   {
//                     k: "A",
//                     c: "\u4e3b\u8981\u80a1\u4e1c\u4e3a\u6cd5\u4eba\u7684\uff0c\u51c0\u8d44\u4ea7\u4e0d\u4f4e\u4e8e3000\u4e07\u5143\u4eba\u6c11\u5e01",
//                   },
//                   {
//                     k: "B",
//                     c: "\u4e3b\u8981\u80a1\u4e1c\u6301\u80a1\u6bd4\u4f8b\u572825%\u4ee5\u4e0a",
//                   },
//                   {
//                     k: "C",
//                     c: "\u4e3b\u8981\u80a1\u4e1c\u6700\u8fd13\u5e74\u6ca1\u6709\u8fdd\u6cd5\u8fdd\u89c4\u884c\u4e3a\u53d7\u5230\u884c\u653f\u5904\u7f5a",
//                   },
//                   {
//                     k: "D",
//                     c: "\u53d6\u5f97\u57fa\u91d1\u4ece\u4e1a\u8d44\u683c\u7684\u62df\u4efb\u9ad8\u7ea7\u7ba1\u7406\u4eba\u5458\u3001\u4e1a\u52a1\u4eba\u5458\u4e0d\u5c11\u4e8e15\u4eba",
//                   },
//                 ],
//                 a: ["A", "B", "C", "D"],
//               },
//               ans: "\u4e3b\u8981\u80a1\u4e1c\u4e3a\u6cd5\u4eba\u51c0\u8d44\u4ea7\u4e0d\u4f4e\u4e8e2\u4ebf\u5143\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//           ],
//         },
//         "3": {
//           type: "3",
//           title: "\u5224\u65ad\u9898",
//           list: [
//             {
//               id: 826,
//               s: "s3",
//               t: 3,
//               qt: 3,
//               qd: 0,
//               q: "\u5173\u4e8e\u8bbe\u7acb\u7ba1\u7406\u516c\u5f00\u52df\u96c6\u57fa\u91d1\u7684\u57fa\u91d1\u7ba1\u7406\u516c\u53f8\u6761\u4ef6\uff0c\u4ee5\u4e0b\u8868\u8ff0\u9519\u8bef\u7684\u662f\uff08 \uff09\u3002",
//               oa: {
//                 opt: [
//                   { k: "t", c: "\u80af\u5b9a\u9009\u9879\u5185\u5bb9" },
//                   { k: "f", c: "\u5426\u5b9a\u9009\u9879\u5185\u5bb9" },
//                 ],
//                 a: ["t"],
//               },
//               ans: "\u4e3b\u8981\u80a1\u4e1c\u4e3a\u6cd5\u4eba\u51c0\u8d44\u4ea7\u4e0d\u4f4e\u4e8e2\u4ebf\u5143\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//             {
//               id: 827,
//               s: "s3",
//               t: 3,
//               qt: 3,
//               qd: 0,
//               q: "\u5173\u4e8e\u8bbe\u7acb\u7ba1\u7406\u516c\u5f00\u52df\u96c6\u57fa\u91d1\u7684\u57fa\u91d1\u7ba1\u7406\u516c\u53f8\u6761\u4ef6\uff0c\u4ee5\u4e0b\u8868\u8ff0\u9519\u8bef\u7684\u662f\uff08 \uff09\u3002",
//               oa: {
//                 opt: [
//                   { k: "t", c: "\u80af\u5b9a\u9009\u9879\u5185\u5bb9" },
//                   { k: "f", c: "\u5426\u5b9a\u9009\u9879\u5185\u5bb9" },
//                 ],
//                 a: ["f"],
//               },
//               ans: "\u4e3b\u8981\u80a1\u4e1c\u4e3a\u6cd5\u4eba\u51c0\u8d44\u4ea7\u4e0d\u4f4e\u4e8e2\u4ebf\u5143\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//           ],
//         },
//         "10": {
//           type: "10",
//           title: "\u8ba1\u7b97\u9898",
//           list: [
//             {
//               id: 828,
//               s: "s3",
//               t: 4,
//               qt: 10,
//               qd: 1,
//               q: "\u5206\u522b\u8ba1\u7b972021\u5e74\u30012022\u5e74X\u4ea7\u54c1\u5728\u5b8c\u5168\u6210\u672c\u6cd5\u4e0b\u548c\u53d8\u52a8\u6210\u672c\u6cd5\u4e0b\u7684\u5355\u4f4d\u4ea7\u54c1\u6210\u672c\u3002",
//               oa: ["\u4f1a"],
//               ans: "\u672c\u9898\u8003\u67e5\u7684\u662f\u7528\u5b8c\u5168\u6210\u672c\u6cd5\u548c\u53d8\u52a8\u6210\u672c\u6cd5\u5206\u522b\u8ba1\u7b97\u4ea7\u54c1\u7684\u5355\u4f4d\u6210\u672c\u3002\n\u5b8c\u5168\u6210\u672c\u6cd5\u4e0b\u5355\u4f4d\u4ea7\u54c1\u6210\u672c\n=\u5355\u4f4d\u76f4\u63a5\u6750\u6599+\u5355\u4f4d\u76f4\u63a5\u4eba\u5de5+\u5355\u4f4d\u53d8\u52a8\u5236\u9020\u8d39\u7528+\u5355\u4f4d\u56fa\u5b9a\u5236\u9020\u8d39\u7528\n\u5355\u4f4d\u56fa\u5b9a\u5236\u9020\u8d39\u7528\n=\u56fa\u5b9a\u5236\u9020\u8d39\u7528\u603b\u989d\u00f7\u672c\u5e74\u4ea7\u91cf\n2021\u5e74\u5b8c\u5168\u6210\u672c\u6cd5\u4e0b\u5355\u4f4d\u4ea7\u54c1\u6210\u672c\n=\u5355\u4f4d\u76f4\u63a5\u6750\u6599+\u5355\u4f4d\u76f4\u63a5\u4eba\u5de5+\u5355\u4f4d\u53d8\u52a8\u5236\u9020\u8d39\u7528+\u56fa\u5b9a\u5236\u9020\u8d39\u7528\u603b\u989d\u00f7\u672c\u5e74\u4ea7\u91cf\n=6+7+2+1860000\u00f762000=45\uff08\u5143/\u4ef6\uff09\n2022\u5e74\u5b8c\u5168\u6210\u672c\u6cd5\u4e0b\u5355\u4f4d\u4ea7\u54c1\u6210\u672c\n=\u5355\u4f4d\u76f4\u63a5\u6750\u6599+\u5355\u4f4d\u76f4\u63a5\u4eba\u5de5+\u5355\u4f4d\u53d8\u52a8\u5236\u9020\u8d39\u7528+\u56fa\u5b9a\u5236\u9020\u8d39\u7528\u603b\u989d\u00f7\u672c\u5e74\u4ea7\u91cf\n=6+7+2+1860000\u00f750000=52.2\uff08\u5143/\u4ef6\uff09\n\u53d8\u52a8\u6210\u672c\u6cd5\u4e0b\u5355\u4f4d\u4ea7\u54c1\u6210\u672c\n=\u5355\u4f4d\u76f4\u63a5\u6750\u6599+\u5355\u4f4d\u76f4\u63a5\u4eba\u5de5+\u5355\u4f4d\u53d8\u52a8\u5236\u9020\u8d39\u7528\n2021\u5e74\u53d8\u52a8\u6210\u672c\u6cd5\u4e0b\u5355\u4f4d\u4ea7\u54c1\u6210\u672c\n=6+7+2=15\uff08\u5143/\u4ef6\uff09\n2022\u5e74\u53d8\u52a8\u6210\u672c\u6cd5\u4e0b\u5355\u4f4d\u4ea7\u54c1\u6210\u672c\n=6+7+2=15\uff08\u5143/\u4ef6\uff09",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//           ],
//         },
//         "9": {
//           type: "9",
//           title: "\u6848\u4f8b\u5206\u6790\u9898",
//           list: [
//             {
//               id: 829,
//               s: "s3",
//               t: 4,
//               qt: 9,
//               qd: 7,
//               q: "\u674e\u67d0\u548c\u738b\u67d0\u5173\u4e8e\u8fd4\u8fd8\u62bd\u9003\u51fa\u8d44\u7684\u4e49\u52a1\u201c\u5df2\u8d85\u8fc7\u8bc9\u8bbc\u65f6\u6548\u201d\u7684\u6297\u8fa9\u662f\u5426\u6210\u7acb\uff1f\u5e76\u8bf4\u660e\u7406\u7531\u3002",
//               oa: ["\u4f1a"],
//               ans: "\u672c\u9898\u8003\u67e5\u7684\u662f\u503a\u52a1\u4eba\u8d22\u4ea7\u7684\u6536\u56de\u3002\u6839\u636e\u300a\u516c\u53f8\u6cd5\u300b\u89c4\u5b9a\uff0c\u503a\u52a1\u4eba\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u7684\uff0c\u7ba1\u7406\u4eba\u5e94\u5f53\u8981\u6c42\u8be5\u51fa\u8d44\u4eba\u7f34\u7eb3\u6240\u8ba4\u7f34\u7684\u51fa\u8d44\uff0c\u800c\u4e0d\u53d7\u51fa\u8d44\u671f\u9650\u7684\u9650\u5236\u3002\u5e7f\u4e49\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u5305\u62ec\u51fa\u8d44\u540e\u53c8\u62bd\u9003\u51fa\u8d44\u7684\u60c5\u51b5\u3002\u7ba1\u7406\u4eba\u4ee3\u8868\u503a\u52a1\u4eba\u63d0\u8d77\u8bc9\u8bbc\uff0c\u4e3b\u5f20\u51fa\u8d44\u4eba\u5411\u503a\u52a1\u4eba\u4f9d\u6cd5\u7f34\u4ed8\u672a\u5c65\u884c\u7684\u51fa\u8d44\u6216\u8005\u8fd4\u8fd8\u62bd\u9003\u7684\u51fa\u8d44\u672c\u606f\uff0c\u51fa\u8d44\u4eba\u4ee5\u8ba4\u7f34\u51fa\u8d44\u5c1a\u672a\u5c4a\u81f3\u516c\u53f8\u7ae0\u7a0b\u89c4\u5b9a\u7684\u7f34\u7eb3\u671f\u9650\u6216\u8005\u8fdd\u53cd\u51fa\u8d44\u4e49\u52a1\u5df2\u7ecf\u8d85\u8fc7\u8bc9\u8bbc\u65f6\u6548\u4e3a\u7531\u6297\u8fa9\u7684\uff0c\u4eba\u6c11\u6cd5\u9662\u4e0d\u4e88\u652f\u6301\u3002\u672c\u9898\u4e2d\uff0c\u674e\u67d0\u548c\u738b\u67d0\u7684\u884c\u4e3a\u5c5e\u4e8e\u62bd\u9003\u51fa\u8d44\uff0c\u7ba1\u7406\u4eba\u4e3b\u5f20\u5176\u5c65\u884c\u8fd4\u8fd8\u62bd\u9003\u51fa\u8d44\u7684\u4e49\u52a1\u4e0d\u53d7\u8bc9\u8bbc\u65f6\u6548\u7684\u9650\u5236\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//             {
//               id: 830,
//               s: "s3",
//               t: 4,
//               qt: 9,
//               qd: 7,
//               q: "\u7532\u516c\u53f8\u62b5\u62bc\u7ed9\u4e59\u94f6\u884c\u76845\u53f0\u5de5\u7a0b\u673a\u68b0\u662f\u5426\u5c5e\u4e8e\u503a\u52a1\u4eba\u8d22\u4ea7\uff1f\u5e76\u8bf4\u660e\u7406\u7531\u3002",
//               oa: ["\u4f1a"],
//               ans: "\u672c\u9898\u8003\u67e5\u7684\u662f\u503a\u52a1\u4eba\u8d22\u4ea7\u7684\u6536\u56de\u3002\u6839\u636e\u300a\u516c\u53f8\u6cd5\u300b\u89c4\u5b9a\uff0c\u503a\u52a1\u4eba\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u7684\uff0c\u7ba1\u7406\u4eba\u5e94\u5f53\u8981\u6c42\u8be5\u51fa\u8d44\u4eba\u7f34\u7eb3\u6240\u8ba4\u7f34\u7684\u51fa\u8d44\uff0c\u800c\u4e0d\u53d7\u51fa\u8d44\u671f\u9650\u7684\u9650\u5236\u3002\u5e7f\u4e49\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u5305\u62ec\u51fa\u8d44\u540e\u53c8\u62bd\u9003\u51fa\u8d44\u7684\u60c5\u51b5\u3002\u7ba1\u7406\u4eba\u4ee3\u8868\u503a\u52a1\u4eba\u63d0\u8d77\u8bc9\u8bbc\uff0c\u4e3b\u5f20\u51fa\u8d44\u4eba\u5411\u503a\u52a1\u4eba\u4f9d\u6cd5\u7f34\u4ed8\u672a\u5c65\u884c\u7684\u51fa\u8d44\u6216\u8005\u8fd4\u8fd8\u62bd\u9003\u7684\u51fa\u8d44\u672c\u606f\uff0c\u51fa\u8d44\u4eba\u4ee5\u8ba4\u7f34\u51fa\u8d44\u5c1a\u672a\u5c4a\u81f3\u516c\u53f8\u7ae0\u7a0b\u89c4\u5b9a\u7684\u7f34\u7eb3\u671f\u9650\u6216\u8005\u8fdd\u53cd\u51fa\u8d44\u4e49\u52a1\u5df2\u7ecf\u8d85\u8fc7\u8bc9\u8bbc\u65f6\u6548\u4e3a\u7531\u6297\u8fa9\u7684\uff0c\u4eba\u6c11\u6cd5\u9662\u4e0d\u4e88\u652f\u6301\u3002\u672c\u9898\u4e2d\uff0c\u674e\u67d0\u548c\u738b\u67d0\u7684\u884c\u4e3a\u5c5e\u4e8e\u62bd\u9003\u51fa\u8d44\uff0c\u7ba1\u7406\u4eba\u4e3b\u5f20\u5176\u5c65\u884c\u8fd4\u8fd8\u62bd\u9003\u51fa\u8d44\u7684\u4e49\u52a1\u4e0d\u53d7\u8bc9\u8bbc\u65f6\u6548\u7684\u9650\u5236\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//             {
//               id: 831,
//               s: "s3",
//               t: 4,
//               qt: 9,
//               qd: 7,
//               q: "\u7ba1\u7406\u4eba\u80fd\u5426\u53d6\u56de\u9500\u552e\u7ed9\u4e19\u516c\u53f8\u7684\u4ea7\u54c1\uff1f\u5e76\u8bf4\u660e\u7406\u7531\u3002",
//               oa: ["\u4f1a"],
//               ans: "\u672c\u9898\u8003\u67e5\u7684\u662f\u503a\u52a1\u4eba\u8d22\u4ea7\u7684\u6536\u56de\u3002\u6839\u636e\u300a\u516c\u53f8\u6cd5\u300b\u89c4\u5b9a\uff0c\u503a\u52a1\u4eba\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u7684\uff0c\u7ba1\u7406\u4eba\u5e94\u5f53\u8981\u6c42\u8be5\u51fa\u8d44\u4eba\u7f34\u7eb3\u6240\u8ba4\u7f34\u7684\u51fa\u8d44\uff0c\u800c\u4e0d\u53d7\u51fa\u8d44\u671f\u9650\u7684\u9650\u5236\u3002\u5e7f\u4e49\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u5305\u62ec\u51fa\u8d44\u540e\u53c8\u62bd\u9003\u51fa\u8d44\u7684\u60c5\u51b5\u3002\u7ba1\u7406\u4eba\u4ee3\u8868\u503a\u52a1\u4eba\u63d0\u8d77\u8bc9\u8bbc\uff0c\u4e3b\u5f20\u51fa\u8d44\u4eba\u5411\u503a\u52a1\u4eba\u4f9d\u6cd5\u7f34\u4ed8\u672a\u5c65\u884c\u7684\u51fa\u8d44\u6216\u8005\u8fd4\u8fd8\u62bd\u9003\u7684\u51fa\u8d44\u672c\u606f\uff0c\u51fa\u8d44\u4eba\u4ee5\u8ba4\u7f34\u51fa\u8d44\u5c1a\u672a\u5c4a\u81f3\u516c\u53f8\u7ae0\u7a0b\u89c4\u5b9a\u7684\u7f34\u7eb3\u671f\u9650\u6216\u8005\u8fdd\u53cd\u51fa\u8d44\u4e49\u52a1\u5df2\u7ecf\u8d85\u8fc7\u8bc9\u8bbc\u65f6\u6548\u4e3a\u7531\u6297\u8fa9\u7684\uff0c\u4eba\u6c11\u6cd5\u9662\u4e0d\u4e88\u652f\u6301\u3002\u672c\u9898\u4e2d\uff0c\u674e\u67d0\u548c\u738b\u67d0\u7684\u884c\u4e3a\u5c5e\u4e8e\u62bd\u9003\u51fa\u8d44\uff0c\u7ba1\u7406\u4eba\u4e3b\u5f20\u5176\u5c65\u884c\u8fd4\u8fd8\u62bd\u9003\u51fa\u8d44\u7684\u4e49\u52a1\u4e0d\u53d7\u8bc9\u8bbc\u65f6\u6548\u7684\u9650\u5236\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//             {
//               id: 832,
//               s: "s3",
//               t: 4,
//               qt: 9,
//               qd: 7,
//               q: "\u7532\u516c\u53f8\u62d6\u6b20\u7684\u804c\u5de5\u57fa\u672c\u517b\u8001\u4fdd\u9669\u8d39\u7528\u662f\u5426\u9700\u8981\u8fdb\u884c\u503a\u6743\u7533\u62a5\uff1f\u7ba1\u7406\u4eba\u5bf9\u8be5\u503a\u6743\u5e94\u5982\u4f55\u64cd\u4f5c\uff1f",
//               oa: ["\u4f1a"],
//               ans: "\u672c\u9898\u8003\u67e5\u7684\u662f\u503a\u52a1\u4eba\u8d22\u4ea7\u7684\u6536\u56de\u3002\u6839\u636e\u300a\u516c\u53f8\u6cd5\u300b\u89c4\u5b9a\uff0c\u503a\u52a1\u4eba\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u7684\uff0c\u7ba1\u7406\u4eba\u5e94\u5f53\u8981\u6c42\u8be5\u51fa\u8d44\u4eba\u7f34\u7eb3\u6240\u8ba4\u7f34\u7684\u51fa\u8d44\uff0c\u800c\u4e0d\u53d7\u51fa\u8d44\u671f\u9650\u7684\u9650\u5236\u3002\u5e7f\u4e49\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u5305\u62ec\u51fa\u8d44\u540e\u53c8\u62bd\u9003\u51fa\u8d44\u7684\u60c5\u51b5\u3002\u7ba1\u7406\u4eba\u4ee3\u8868\u503a\u52a1\u4eba\u63d0\u8d77\u8bc9\u8bbc\uff0c\u4e3b\u5f20\u51fa\u8d44\u4eba\u5411\u503a\u52a1\u4eba\u4f9d\u6cd5\u7f34\u4ed8\u672a\u5c65\u884c\u7684\u51fa\u8d44\u6216\u8005\u8fd4\u8fd8\u62bd\u9003\u7684\u51fa\u8d44\u672c\u606f\uff0c\u51fa\u8d44\u4eba\u4ee5\u8ba4\u7f34\u51fa\u8d44\u5c1a\u672a\u5c4a\u81f3\u516c\u53f8\u7ae0\u7a0b\u89c4\u5b9a\u7684\u7f34\u7eb3\u671f\u9650\u6216\u8005\u8fdd\u53cd\u51fa\u8d44\u4e49\u52a1\u5df2\u7ecf\u8d85\u8fc7\u8bc9\u8bbc\u65f6\u6548\u4e3a\u7531\u6297\u8fa9\u7684\uff0c\u4eba\u6c11\u6cd5\u9662\u4e0d\u4e88\u652f\u6301\u3002\u672c\u9898\u4e2d\uff0c\u674e\u67d0\u548c\u738b\u67d0\u7684\u884c\u4e3a\u5c5e\u4e8e\u62bd\u9003\u51fa\u8d44\uff0c\u7ba1\u7406\u4eba\u4e3b\u5f20\u5176\u5c65\u884c\u8fd4\u8fd8\u62bd\u9003\u51fa\u8d44\u7684\u4e49\u52a1\u4e0d\u53d7\u8bc9\u8bbc\u65f6\u6548\u7684\u9650\u5236\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//             {
//               id: 833,
//               s: "s3",
//               t: 4,
//               qt: 9,
//               qd: 2,
//               q: "\u8d75\u67d0\u5e94\u5f53\u5411\u4eba\u6c11\u6cd5\u9662\u63d0\u8d77\u4f55\u79cd\u7c7b\u578b\u7684\u8bc9\u8bbc\uff1f\u8be5\u8bc9\u8bbc\u7684\u88ab\u544a\u662f\u8c01\uff1f",
//               oa: ["\u4f1a"],
//               ans: "\u672c\u9898\u8003\u67e5\u7684\u662f\u503a\u52a1\u4eba\u8d22\u4ea7\u7684\u6536\u56de\u3002\u6839\u636e\u300a\u516c\u53f8\u6cd5\u300b\u89c4\u5b9a\uff0c\u503a\u52a1\u4eba\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u7684\uff0c\u7ba1\u7406\u4eba\u5e94\u5f53\u8981\u6c42\u8be5\u51fa\u8d44\u4eba\u7f34\u7eb3\u6240\u8ba4\u7f34\u7684\u51fa\u8d44\uff0c\u800c\u4e0d\u53d7\u51fa\u8d44\u671f\u9650\u7684\u9650\u5236\u3002\u5e7f\u4e49\u7684\u51fa\u8d44\u4eba\u5c1a\u672a\u5b8c\u5168\u5c65\u884c\u51fa\u8d44\u4e49\u52a1\u5305\u62ec\u51fa\u8d44\u540e\u53c8\u62bd\u9003\u51fa\u8d44\u7684\u60c5\u51b5\u3002\u7ba1\u7406\u4eba\u4ee3\u8868\u503a\u52a1\u4eba\u63d0\u8d77\u8bc9\u8bbc\uff0c\u4e3b\u5f20\u51fa\u8d44\u4eba\u5411\u503a\u52a1\u4eba\u4f9d\u6cd5\u7f34\u4ed8\u672a\u5c65\u884c\u7684\u51fa\u8d44\u6216\u8005\u8fd4\u8fd8\u62bd\u9003\u7684\u51fa\u8d44\u672c\u606f\uff0c\u51fa\u8d44\u4eba\u4ee5\u8ba4\u7f34\u51fa\u8d44\u5c1a\u672a\u5c4a\u81f3\u516c\u53f8\u7ae0\u7a0b\u89c4\u5b9a\u7684\u7f34\u7eb3\u671f\u9650\u6216\u8005\u8fdd\u53cd\u51fa\u8d44\u4e49\u52a1\u5df2\u7ecf\u8d85\u8fc7\u8bc9\u8bbc\u65f6\u6548\u4e3a\u7531\u6297\u8fa9\u7684\uff0c\u4eba\u6c11\u6cd5\u9662\u4e0d\u4e88\u652f\u6301\u3002\u672c\u9898\u4e2d\uff0c\u674e\u67d0\u548c\u738b\u67d0\u7684\u884c\u4e3a\u5c5e\u4e8e\u62bd\u9003\u51fa\u8d44\uff0c\u7ba1\u7406\u4eba\u4e3b\u5f20\u5176\u5c65\u884c\u8fd4\u8fd8\u62bd\u9003\u51fa\u8d44\u7684\u4e49\u52a1\u4e0d\u53d7\u8bc9\u8bbc\u65f6\u6548\u7684\u9650\u5236\u3002",
//               pk: "",
//               pn: "",
//               p: "",
//             },
//           ],
//         },
//       },
//       qd: {
//         "7": [
//           { label: "\u6750\u65991", text: "\u6750\u6599\u6d4b\u8bd51" },
//           {
//             label: "\u6750\u65992",
//             text: "\u6750\u6599\u4e0a\u4f20\u6d4b\u8bd52",
//           },
//         ],
//       },
//       scores: {
//         "824": "2",
//         "825": "10",
//         "826": "7",
//         "827": "7",
//         "828": "10",
//         "829": "20",
//         "830": "10",
//         "831": "10",
//         "832": "12",
//         "833": "12",
//       },
//       qv_no: "qv5",
//       question_total: 10,
//     },
//   };
// });
