import { useDataStore } from "@/pages/exam/store/dataStore";
import { Popover, Radio, RadioGroup } from "@arco-design/web-vue";
import { defineComponent } from "vue";
export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    return () => (
      <div>
        <Popover position="bottom">
          {{
            default: () => (
              <div class=" hover">
                <svg
                  // @ts-ignore
                  t="1721702940578"
                  class=" w-7 h-7 pointer-events-none"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="6421"
                  width="64"
                  height="64"
                >
                  <path
                    d="M609.28 232.32H152.96V404.48h100.48V332.8h127.36v417.92H306.56v100.48h249.6v-100.48H481.28V332.8h128v71.68h100.48V232.32H609.28z"
                    fill="#ffffff"
                    p-id="6422"
                  ></path>
                  <path
                    d="M812.8 492.16H547.84v99.84h58.24V550.4h74.24v242.56h-42.88v58.24H782.08v-58.24h-43.52V550.4h74.24v41.6h58.24V492.16h-58.24z"
                    fill="#ffffff"
                    p-id="6423"
                  ></path>
                </svg>
              </div>
            ),
            content: () => (
              <RadioGroup v-model={dataStore.fontSize} type="button">
                <Radio value={14}>小</Radio>
                <Radio value={16}>中</Radio>
                <Radio value={18}>大</Radio>
              </RadioGroup>
            ),
          }}
        </Popover>
      </div>
    );
  },
});
