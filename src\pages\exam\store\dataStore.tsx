import {
  Computer_getpaper_api,
  Computer_getpaperinfo_api,
  Computer_Submit_api,
} from "@/common/apis/apis";
import { Button, Checkbox, Message, Modal, Notification } from "@arco-design/web-vue";
import { isEmpty } from "lodash";
import { defineStore } from "pinia";
import { ref } from "vue";
import { Question } from "./model/question_model";
import { RouteLocationNormalizedLoaded, useRoute } from "vue-router";
import { getTitle, getType, getUserPositionCookie } from "@common/cookies/user.ts";

export const useDataStore = defineStore("dataStore", {
  state: () => {
    return {
      // 试卷数据源
      testPaperSource: undefined as Computer_getpaperinfo_api.Model | undefined,
      // 题数据源
      questionSource: undefined as Computer_getpaper_api.Model | undefined,
      // 题列表
      questionList: [] as Question.BaseQuestionModel[],
      // 当前题下标
      currentIndex: 0,
      fontSize: 16,

      route: {} as RouteLocationNormalizedLoaded,

      // 时间戳
      enterTimestamp: Math.round(Date.now() / 1000),

      isLock: false,
      isSubmit: false,
      timer: null as any,
      provinceTitle: getTitle(),
      used_time: 0,
      // 防止重复弹窗标记
      hasTriggeredAutoSubmit: false,
      // 是否已有弹窗显示（防止同时出现多个弹窗）
      hasModalShown: false,
    };
  },
  getters: {
    // 获取所有题分组
    allQuestionGroupList: (state) => {
      return Object.values(state.questionSource?.questions ?? {}).map((e) => {
        return {
          typeName: e.title,
          type: e.type,
          group_id: e.id,
          list: state.questionList.filter((e2) => {
            return e.id == e2.group_id;
          }),
        };
      });
    },
    // 当前题
    currentQuestion: (state) => {
      const temp = state.questionList[state.currentIndex];
      if (isEmpty(temp)) return null;
      return temp;
    },
  },
  actions: {
    async init() {
      this.route = useRoute();

      const examkey = this.route.params.examKey as string;
      const type = getType();

      try {
        const res = await Promise.all([
          // 试卷信息
          Computer_getpaperinfo_api.api({ data: { examkey, type: type } }),
          // 题数据
          Computer_getpaper_api.api({ data: { examkey: examkey, type: type } }),
        ]);
        // 保存一下试卷信息
        this.testPaperSource = res[0];

        // 测试自动提交
        if (window.location.href.indexOf("debug") > -1) {
          res[0].test_time = "10"; // 测试自动提交
        }

        // 保存一下题数据
        this.questionSource = res[1];
        // 处理所有题
        this._motherFuckerAllQuestion();
      } catch (e) {
        console.error(e);
        Notification.error({
          content: (e as Error).message,
        });
      }
    },
    // 处理所有题
    _motherFuckerAllQuestion() {
      // 把所有题拆成一个大组
      const allQuestionTemp: Computer_getpaper_api.QuestionsItemModel[] = [];
      Object.values(this.questionSource?.questions ?? {}).forEach((e) => {
        e.list.forEach((e2) => {
          allQuestionTemp.push(e2);
        });
      });
      this.questionList = allQuestionTemp.map<Question.BaseQuestionModel>((e, i) => {
        // 1=单项选择题 2=多项选择题 3=判断题 9=案例分析题 10=计算题
        const commonProps: Question.IProps = {
          question: e,
          index: i + 1,
          score: this._getScoreByQuestionId(e.id),
          materialList: this._getMaterialByQuestionId(e.qd),
          cost_time: 0,
        };
        switch (e.t) {
          // 单选题
          case 1:
            return new Question.SingleChoiceQuestionModel(commonProps);
          // 多选题
          case 2:
            return new Question.MultipleChoiceQuestionModel(commonProps);
          // 判断题
          case 3:
            return new Question.CheckQuestionModel(commonProps);
          // 简答题
          case 4:
            return new Question.WritingQuestionModel(commonProps);
          default:
            console.error(`还有题没实现${JSON.stringify(e)}`);
            return new Question.WritingQuestionModel(commonProps);
        }
      });
      let obj: any = this.questionSource?.answer ? this.questionSource?.answer : {};
      this.used_time = this.questionSource?.used_time ? this.questionSource?.used_time : 0;
      if (Object.keys(obj).length > 0) {
        for (const key in obj) {
          const id = Number(key);
          const answerList: [] = obj[key].a.split(",");
          const cost = Number(obj[key].t);

          const elementToUpdate = this.questionList.find((item) => item.id === id);
          if (elementToUpdate) {
            elementToUpdate.respondList = answerList;
            elementToUpdate.cost_time = cost;
          }
        }
      }
    },
    // 根据题id查找其分数
    _getScoreByQuestionId(id: Computer_getpaper_api.QuestionsItemModel["id"]): number {
      const temp = this.questionSource?.scores[id];

      if (temp) {
        return Number(temp);
      } else {
        return 0;
      }
    },
    // 根据题材料id查找其材料列表
    _getMaterialByQuestionId(
      id: Computer_getpaper_api.QuestionsItemModel["qd"]
    ): Computer_getpaper_api.QuestionsItemMaterialModel[] {
      const qd = this.questionSource?.qd;
      if (isEmpty(qd)) return [];
      const temp = qd[id];
      return temp || [];
    },

    // 计时器
    setTimeCount() {
      this.timer = setInterval(() => {
        if (this.isLock) {
          return;
        }
        const currentQuestion = this.questionList[this.currentIndex];
        if (currentQuestion) {
          currentQuestion.cost_time = currentQuestion.cost_time + 1;
        }
      }, 1000);
    },

    // 切换下标到指定题
    changeCurrentToIndex(value: number) {
      if (value <= 0) {
        return (this.currentIndex = 0);
      }
      if (value >= this.questionList.length - 1) {
        return (this.currentIndex = this.questionList.length - 1);
      }
      this.currentIndex = value;
    },

    // 提交
    async submit() {
      let unCompleteCount: number;

      try {
        unCompleteCount = this.questionList.filter((e) => !e.isAnswerComplete).length;
      } catch (error) {
        unCompleteCount = 0;
      }

      const isConfirm = ref(false);
      const modalRes = Modal.warning({
        title: "提示",
        footer: false,
        content: () => (
          <div class="flex flex-col items-center space-y-5">
            <div class=" text-center text-lg">
              {unCompleteCount ? (
                <span>
                  {" "}
                  还有
                  <span class=" font-bold text-primary">{unCompleteCount}</span>
                  道题
                  <span class="font-bold text-primary">未完成</span>，
                </span>
              ) : (
                ""
              )}
              确定要交卷吗？
            </div>
            <div class="tips" style="opacity: 0.55;margin-top: 5px; margin-bottom: 10px;">
              公文写作作答记录不会保存，请自行复制/截图留存！
            </div>
            <Checkbox v-model={isConfirm.value}>我确认，结束考试，结束后不可再次作答。</Checkbox>
            <div class="fle space-x-10">
              <Button
                onClick={() => {
                  modalRes.close();
                }}
                size="large"
              >
                取消
              </Button>
              <Button
                disabled={!isConfirm.value}
                size="large"
                type="primary"
                onClick={() => {
                  modalRes.close();
                  this.submitInfo();
                }}
              >
                确认
              </Button>
            </div>
          </div>
        ),
      });
    },

    // 重置test_time倒计时，防止触发test_time弹窗
    resetTestTimeCountdown() {
      // 将test_time设置为已用时间，使倒计时归零
      if (this.testPaperSource) {
        this.testPaperSource.test_time = this.used_time.toString();
      }
    },

    // 提交数据（不自动跳转版本，用于end_time倒计时）
    async submitWithoutRedirect() {
      // 防重复提交检查
      if (this.isSubmit || this.hasTriggeredAutoSubmit) {
        throw new Error('Already submitted');
      }

      // 设置标记，防止重复提交和test_time弹窗
      this.hasTriggeredAutoSubmit = true;
      this.resetTestTimeCountdown();

      interface Item {
        a: number;
        g: number;
        r: number;
        s: number;
        t: number;
      }
      const arr = JSON.parse(JSON.stringify(this.questionList));
      const endTimestamp = Math.round(Date.now() / 1000);
      const type: any = getType();
      const result: { [id: number]: Item } = arr.reduce((obj: any, item: any) => {
        const answerList = item.answerList || [];
        const respondList = item.respondList || [];
        if (item.respondList.length > 0) {
          obj[item.id] = {
            qid: item.id,
            a: item.respondList.sort().join(","),
            // g: item.group_id,
            g: 0,
            r: JSON.stringify(answerList.sort()) == JSON.stringify(respondList.sort()) ? 1 : 2,
            s: item.score,
            t: item.cost_time,
          };
        }
        return obj;
      }, {});
      const jobObj = JSON.parse(getUserPositionCookie() || "{}");
      const rec_job_id = jobObj[this.route.params.examKey as any];
      
      try {
        //自行处理提交
        const res = await Computer_Submit_api.api({
          data: {
            answer: result,
            brand: "jbcgk",
            end_time: endTimestamp,
            start_time: this.enterTimestamp,
            paper_id: this.route.params.examKey as string,
            post_type: "submit",
            purpose: "app",
            type: type,
            used_time: endTimestamp - this.enterTimestamp,
            rec_job_id: rec_job_id ? rec_job_id : "",
          },
        });
        
        let shareid = res.data.shareid;
        this.isSubmit = true;
        clearInterval(this.timer);
        
        // 返回shareid和跳转信息，但不自动跳转
        return {
          shareid,
          redirectUrl: this.getRedirectUrl(shareid, type)
        };
      } catch (error) {
        console.error('Submit error:', error);
        throw error;
      }
    },

    // 获取跳转URL的方法
    getRedirectUrl(shareid: string, type: any) {
      const domain = window.location.hostname;
      const brand = document.cookie.replace(/(?:(?:^|.*;\s*)brand\s*\=\s*([^;]*).*$)|^.*$/, "$1");
      const path = `/wa/exam/result?shareid=${shareid}&brand=${brand ? brand : "jbcgk"}&paper_id=${
        this.route.params.examKey as string
      }&type=${type}&purpose=app`;
      return "https://" + domain + path;
    },

    // 提交数据
    async submitInfo() {
      // 防重复提交检查
      if (this.isSubmit || this.hasTriggeredAutoSubmit) {
        return;
      }

      // 设置标记，防止重复操作
      this.hasTriggeredAutoSubmit = true;
      this.resetTestTimeCountdown();

      interface Item {
        a: number;
        g: number;
        r: number;
        s: number;
        t: number;
      }
      const arr = JSON.parse(JSON.stringify(this.questionList));
      const endTimestamp = Math.round(Date.now() / 1000);
      const type: any = getType();
      const result: { [id: number]: Item } = arr.reduce((obj: any, item: any) => {
        const answerList = item.answerList || [];
        const respondList = item.respondList || [];
        if (item.respondList.length > 0) {
          obj[item.id] = {
            qid: item.id,
            a: item.respondList.sort().join(","),
            // g: item.group_id,
            g: 0,
            r: JSON.stringify(answerList.sort()) == JSON.stringify(respondList.sort()) ? 1 : 2,
            s: item.score,
            t: item.cost_time,
          };
        }
        return obj;
      }, {});
      const jobObj = JSON.parse(getUserPositionCookie() || "{}");
      const rec_job_id = jobObj[this.route.params.examKey as any];
      //自行处理提交
      const res = await Computer_Submit_api.api({
        data: {
          answer: result,
          brand: "jbcgk",
          end_time: endTimestamp,
          start_time: this.enterTimestamp,
          paper_id: this.route.params.examKey as string,
          post_type: "submit",
          purpose: "app",
          type: type,
          used_time: endTimestamp - this.enterTimestamp,
          rec_job_id: rec_job_id ? rec_job_id : "",
        },
      });
      let shareid = res.data.shareid;
      Message.success("提交成功");
      this.isSubmit = true;
      clearInterval(this.timer);
      const redirectUrl = this.getRedirectUrl(shareid, type);
      window.location.href = redirectUrl;
    },
  },
});
