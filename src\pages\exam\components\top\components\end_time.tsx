import { useDataStore } from "@/pages/exam/store/dataStore";
import { Countdown, Modal } from "@arco-design/web-vue";
import { defineComponent, computed, ref, onMounted } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();
    
    // 存储跳转URL，用于用户点击确认后跳转
    const redirectUrl = ref<string>("");
    
    // 计算倒计时目标时间
    const endTimeValue = computed(() => {
      const endTime = dataStore.testPaperSource?.end_time;
      if (!endTime) return 0;
      
      // 如果end_time是时间戳格式（10位数字字符串），需要转换为毫秒
      const timestamp = Number(endTime);
      return timestamp * 1000;
    });
    
    return () => {
      // 如果没有end_time数据，不渲染组件
      if (!dataStore.testPaperSource?.end_time) return null;
      
      // 如果已经提交了，不渲染组件
      if (dataStore.isSubmit) return null;
      
      // 不显示倒计时界面，只运行倒计时逻辑
      return (
        <div style={{ display: 'none' }}>
          <Countdown
            key={dataStore.testPaperSource?.end_time}
            value={endTimeValue.value}
            format="HH:mm:ss"
            onFinish={async () => {
              // 检查是否已经有弹窗显示、已提交或已触发自动提交，避免重复操作
              if (dataStore.hasModalShown || dataStore.isSubmit || dataStore.hasTriggeredAutoSubmit) {
                return;
              }
              
              try {
                // 设置弹窗显示标记
                dataStore.hasModalShown = true;
                
                // 倒计时结束，立即调用提交接口（不自动跳转）
                const result = await dataStore.submitWithoutRedirect();
                redirectUrl.value = result.redirectUrl;
                
                // 显示只有确认按钮的弹窗
                const { close } = Modal.open({
                  title: "提示",
                  content: "模考时间截止，已自动交卷",
                  simple: true,
                  okText: "确认",
                  hideCancel: true,
                  escToClose: false,
                  maskClosable: false,
                  onOk() {
                    close();
                    // 点击确认后跳转
                    if (redirectUrl.value) {
                      window.location.href = redirectUrl.value;
                    }
                  },
                });
              } catch (error) {
                // 提交失败时也显示弹窗，但提示失败
                const { close } = Modal.open({
                  title: "提示",
                  content: "考试时间已结束，但提交失败，请联系管理员",
                  simple: true,
                  okText: "确认",
                  hideCancel: true,
                  escToClose: false,
                  maskClosable: false,
                  onOk() {
                    close();
                  },
                });
              }
            }}
          />
        </div>
      );
    };
  },
}); 