// 数字专中文
export const toChinese = (val: any) => {
  const nubList: string[] = [
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
    "十",
  ];
  // 传进来的值，索引从0开始
  let sn: number = parseInt(val) + 1;
  // 用对应的值去换语文数字，并返回数组-1的值
  if (sn <= 10) {
    return nubList[sn - 1];
  } else if (sn <= 100) {
    if (sn < 20) return "十" + nubList[(sn % 10) - 1];
    if (sn % 10 == 0) return nubList[Math.floor(sn / 10) - 1] + "十";
    else
      return nubList[Math.floor(sn / 10) - 1] + "十" + nubList[(sn % 10) - 1];
  }
};

// 秒转00:00:00
export function timeToDoubleLength(seconds: number): string {
  const hour = Number(
    Math.floor(seconds / 3600) >= 10
      ? Math.floor(seconds / 3600)
      : "0" + Math.floor(seconds / 3600)
  );
  seconds -= 3600 * hour;
  const min = Number(
    Math.floor(seconds / 60) >= 10
      ? Math.floor(seconds / 60)
      : "0" + Math.floor(seconds / 60)
  );
  seconds -= 60 * min;
  const sec = seconds >= 10 ? seconds : "0" + seconds;
  return (
    String(hour).padStart(2, "0") +
    ":" +
    String(min).padStart(2, "0") +
    ":" +
    String(sec).padStart(2, "0")
  );
}
