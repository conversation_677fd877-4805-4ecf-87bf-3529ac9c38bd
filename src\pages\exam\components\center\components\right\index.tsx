import { useDataStore } from "@/pages/exam/store/dataStore";
import { Divider } from "@arco-design/web-vue";
import { isEmpty } from "lodash";
import { defineComponent } from "vue";
import Answer from "./components/answer";
import Material from "./components/material";
import Question_title from "./components/question_title";
import Type_title from "./components/type_title";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    return () => (
      <div
        class="flex-1  bg-white p-3  space-y-3"
        style={{
          fontSize: dataStore.fontSize + "px",
          "overflow-y": "auto",
          height: " calc(100vh - 120px)",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Type_title />
        <Divider />
        {!isEmpty(dataStore.currentQuestion?.materialList) && <Material />}
        <div style={{
          "overflow-y": "auto",
          flex: 1,
        }}>
          <Question_title />
          <div class="flex-1 relative">
            <div class="absolute w-full h-full">
              <Answer />
            </div>
          </div>
        </div>
      </div>
    );
  },
});
