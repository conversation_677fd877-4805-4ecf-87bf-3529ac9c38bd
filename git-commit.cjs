const fs = require('fs')
const shell = require('shelljs')
const fileName = '.git-version'

function shellEcho (msg) {
  shell.echo(`================${msg}================`)
}

// 检查是否有未commit的文件
if (shell.exec('git status').stdout.indexOf('working tree clean') === -1) {
  shellEcho('还有未提交的文件')
  shell.exit(1)
}

function getFileText () {
  try {
    return fs.readFileSync(fileName).toString()
  } catch (err) {
    return 0
  }
}

const text = parseInt(getFileText()) + 1
fs.writeFile(fileName, text.toString(), function () {
  if (!shell.which('git')) {
    shellEcho('你还没安装git,请先安装git')
    shell.exit(1)
  }
  if (shell.exec('git status').stdout.indexOf('working tree clean') !== -1) {
    shellEcho('没有变动文件')
    shell.exit(1)
  }
  const intro = process.argv.length >= 3 && process.argv.splice(2)
  if (!intro) {
    shell.echo('请填写提交信息,格式为feat(xxxx):xxxxx')
    shell.exit(1)
  }
  if (shell.exec('git add .').code !== 0) {
    shellEcho('git add执行出错')
    shell.exit(1)
  }
  if (shell.exec(`git commit -m ${intro}`).code !== 0) {
    shellEcho('git  commit执行出错')
    shell.exit(1)
  }
  const BranchName = shell.exec('git rev-parse --abbrev-ref HEAD')
  if (shell.exec(`git pull --tags origin ${BranchName}`).code !== 0) {
    shellEcho('git  pull执行出错')
    shell.exit(1)
  }
  shell.echo(`代码提交到本地完成,当前分支是${BranchName}`)
  if (shell.exec(`git push origin ${BranchName}`).code !== 0) {
    shellEcho('推送远程失败')
    shell.exit(1)
  }
  shellEcho('推送远程完成')
})
