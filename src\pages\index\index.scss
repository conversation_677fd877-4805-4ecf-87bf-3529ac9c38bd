body {
  --border-radius-medium: 10px;
  --color-fill-2: #ffffff;
  --border-radius-small: 5px;
  .border-gray-200 {
    border-color: #000;
  }
  .arco-modal-simple {
    padding: 2rem;
  }
  .arco-btn-size-large {
    padding: 0 23px;
    height: 38px;
    font-size: 16px;
    letter-spacing: 3px;
  }
  .arco-input-wrapper .arco-input.arco-input-size-large {
    font-size: 16px;
  }
  .arco-btn-secondary,
  .arco-btn-secondary[type="button"],
  .arco-btn-secondary[type="submit"] {
    background-color: #8c93ac;
    color: #fff;
  }
  .arco-descriptions-item-label-block {
    color: #333;
    width: 75px;
    text-align: justify !important;
    text-align-last: justify !important;
  }
  .arco-descriptions-size-medium .arco-descriptions-item-label-block,
  .arco-descriptions-size-medium .arco-descriptions-item-value-block {
    font-size: 16px;
    padding-bottom: 14px;
  }
}

.content-box {
  padding: 50px 50px 30px 50px;
  .title-t {
    font-size: 18px;
    padding-left: 10px;
    position: relative;
    color: #393F47;
    &::after {
      position: absolute;
      content: " ";
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background: #2588E8;
    }
  }

  .mt32 {
    margin-top: 32px;
  }

  .select-list {
    width: 350px;
  }

 

  .select-list-item {
    width: 350px;
    border: 1px solid #EBEBEB;
    border-radius: 6px;
    margin-bottom: 16px;
    background: #fff;
  
    &:last-child {
      margin-bottom: 0;
    }
    &:hover{
      background: none;
      border: 1px solid #EBEBEB;
    }
    .bottom-box {
      margin-top: 32px;
    }
    &.arco-select-view-disabled {
      background: #fff;
      border: 1px solid #EBEBEB;
      &:hover{
        background: none;
        border: 1px solid #EBEBEB;
      }
    }
  }

  .main-content {
    padding-bottom: 32px;
    border-bottom: 1px solid #F5F5F5;
    .fonts {
      margin-left: 32px;
      margin-right: 0;
      padding-right: 20px;
    }
  }

  .btn-s {
    width: 120px;
    height: 40px;
    font-size: 14px;
    border-radius: 8px;
    margin: 0 25px;
    letter-spacing: 0;
    font-weight: bold;
  }

  .mt32 {
    margin-top: 32px;
  }
 
}

.flex1 {
  flex: 1;
}

.cor-f {
  color: #fff !important;
  font-size: 40px !important;
  text-align: center;
  margin-bottom: 40px;
}

.bottom-b {
  background: rgba(255, 255, 255, 0.3);
  margin-top: 40px;
  color: rgba(95, 126, 149, 1) !important;
}

.w140 {
  width: 140px;
}

.no-job {
  min-width: 360px;
  max-width: 650px;
  margin: 0 auto;
  flex: none;
}

.btn-240 {
  width: 240px !important;
  font-size: 14px;
  font-weight: bold;
}

.select-wrapper {
  margin-bottom: 16px;
  position: relative;
  z-index: 222;
  &:last-child{
    margin-bottom: 0;
  }
}

.el-select__wrapper.is-disabled {
  background: #fff;
}

.w-1000 {
  width: 1000px !important;
}


.w-100 {
  width: 100px;
}

.arco-divider-horizontal {
  display: none;
}