import { useDataStore } from "@/pages/exam/store/dataStore";
import { Question } from "@/pages/exam/store/model/question_model";
import { ResizeBox, Tabs } from "@arco-design/web-vue";
import { computed, defineComponent } from "vue";

// 材料
export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    const currentQuestion = computed<Question.WritingQuestionModel | undefined>(
      () => dataStore.currentQuestion as Question.WritingQuestionModel
    );

    return () => (
      <ResizeBox
        directions={["bottom"]}
        style={{
          height: "220px",
          maxHeight: "400px",
        }}
      >
        <div class="h-full relative">
          <div class="absolute w-full h-full overflow-y-auto">
            <Tabs type="card-gutter">
              {currentQuestion.value?.materialList.map((e, i) => {
                return (
                  <Tabs.TabPane key={i} title={"材料" + (Number(i) + Number(1))}>
                    <div
                      class="overflow-y-auto p-3 user-select-none"
                      style="-webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; white-space:pre-wrap;"
                      v-html={e.text.replace(/\n/g, "")}
                    ></div>
                  </Tabs.TabPane>
                );
              })}
            </Tabs>
          </div>
        </div>
      </ResizeBox>
    );
  },
});
