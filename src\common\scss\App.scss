@tailwind base;
@tailwind components;
@tailwind utilities;
@import "@arco-design/web-vue/dist/arco.css";
@import "./iconfont.scss";

html {
  background-color: #f1f5fb;
}

body {
  --primary-6: 0, 123, 255;
  font-family: "Microsoft YaHei", "宋体";
  --border-radius-small: 5px;
}

/* 滚动整体 */
::-webkit-scrollbar {
  width: 10px;
}
ul,
li {
  list-style: none;
}

// 滚动槽
::-webkit-scrollbar-track {
  background-color: rgba($color: #eeeeee, $alpha: 0.5);
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: rgba($color: #ccc, $alpha: 1);
}

// 隐藏滚动条
.hideScrollbar::-webkit-scrollbar {
  display: none;
}

svg {
  display: inline;
  vertical-align: baseline;
}
.hover {
  @apply cursor-pointer hover:scale-95 transition-transform;
}

.letterspacing3 {
  letter-spacing: 3px;
}
.bg-primary {
  background: #2588e8 !important;
}
.arco-btn-primary {
  background: #2588e8 !important;
}

.arco-btn-primary.arco-btn-disabled {
  background: #2588e8 !important;
  opacity: 0.3;
}
.user-select-none {
  user-select: none;
}
