import { useDataStore } from "@/pages/exam/store/dataStore";
import { Countdown, Modal } from "@arco-design/web-vue";
import { defineComponent, ref } from "vue";
import { getExam, getProvince } from "@common/cookies/user.ts";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();
    // 倒计时函数
    const countdown = ref(60);
    let intervalId: any = null;

    function countdownText() {
      if (countdown.value > 0) {
        return `考试时间已结束，将在 ${countdown.value}s 后自动交卷`;
      } else {
        return "考试时间已结束，将在 0s 后自动交卷";
      }
    }
    const startCountdown = () => {
      intervalId = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value -= 1;
        }
        if (countdown.value == 0) {
          countdown.value = -1;
          dataStore.submitInfo();
          clearInterval(intervalId);
        }
      }, 1000);
    };

    return () => (
      <div class=" text-primary bg-white rounded-full px-2 py-1 ">
        <Countdown
          key={dataStore.testPaperSource?.test_time}
          value={
            Date.now() +
            (Number(dataStore.testPaperSource?.test_time ?? 0) - dataStore.used_time) * 1000
          }
          format="剩余：H 时 m 分 s 秒"
          onFinish={() => {
            // 检查是否已经有弹窗或已提交，避免重复弹窗
            if (dataStore.hasModalShown || dataStore.isSubmit || dataStore.hasTriggeredAutoSubmit) {
              return;
            }

            // 设置弹窗显示标记，防止end_time弹窗同时出现
            dataStore.hasModalShown = true;

            
            // 定义倒计时的计算属性
            startCountdown();
            const { close } = Modal.open({
              title: "提示",
              content: countdownText,
              simple: true,
              okText: "立即交卷",
              cancelText: "放弃交卷",
              escToClose: false,
              maskClosable: false,
              onBeforeCancel(): any {
                const { close: closeTwo } = Modal.open({
                  title: "提示",
                  content: "确认放弃交卷吗，本次作答记录将不会保存",
                  simple: true,
                  okText: "确定",
                  cancelText: "取消",
                  escToClose: false,
                  maskClosable: false,
                  onCancel() {
                    closeTwo();
                  },
                  onOk() {
                    close();
                    closeTwo();
                    dataStore.isSubmit = true;
                    let exam = getExam();
                    let province = getProvince();
                    const domain = window.location.hostname;
                    const brand = document.cookie.replace(
                      /(?:(?:^|.*;\s*)brand\s*\=\s*([^;]*).*$)|^.*$/,
                      "$1"
                    );
                    const path = `/wp/practice/exam_detail?brand=${brand ? brand : "jbcgk"}&province=${province}&exam=${exam}&paper_id=${
                      dataStore.route.params.examKey as string
                    }`;
                    window.location.href = "https://" + domain + path;
                  },
                });
              },
              onOk() {
                close();
                dataStore.submitInfo();
              },
            });
          }}
          valueStyle={{
            fontSize: "14px",
            color: "#2382EF",
          }}
        />
      </div>
    );
  },
});
