apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx-internal-jinbiaochi
  name: ${DEPLOYMENT_NAME}-internal-jinbiaochi
  namespace: ${NAMESPACE}
spec:
  rules:
  - host: ${INTERNAL_JINBIAOCHI_DOMAIN}
    http:
      paths:
      - backend:
          serviceName: ${DEPLOYMENT_NAME}
          servicePort: ${SERVICE_PORT}
        path: /${URI_PREFIX_S}/mp