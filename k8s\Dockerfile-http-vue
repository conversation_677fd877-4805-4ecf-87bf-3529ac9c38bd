FROM ccr.ccs.tencentyun.com/tiku/node:lts-alpine3.19 as build-stage
ARG pro
RUN mkdir /src
WORKDIR /src
COPY . .
RUN npm config set registry https://registry.npmmirror.com
RUN npm install --global pnpm && pnpm install && pnpm run build:${pro}

FROM ccr.ccs.tencentyun.com/tiku/nginx
ARG uri_prefix
COPY --from=build-stage /src/dist/ /usr/share/nginx/html/
RUN sed -i "9a\try_files \$uri \$uri/ ${uri_prefix}/index.html;" /etc/nginx/conf.d/default.conf


