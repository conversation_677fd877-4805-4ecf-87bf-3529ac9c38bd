import { defineComponent, ref } from "vue";
import Answer_sheet from "./components/answer_sheet";
import User_info from "./components/user_info";

export default defineComponent({
  setup() {
    return () => (
      <BuildDisplay>
        <div class={`bg-white h-full w-60 p-3 space-y-3 flex flex-col`}>
          <User_info />
          <Answer_sheet />
        </div>
      </BuildDisplay>
    );
  },
});

// 展开缩小
const BuildDisplay = defineComponent({
  setup(_, ctx) {
    // 是否打开
    const isDisplay = ref(true);
    return () => (
      <div class="relative h-full">
        {isDisplay.value && ctx.slots.default && ctx.slots.default()}
        <div
          onClick={() => {
            isDisplay.value = !isDisplay.value;
          }}
          class="absolute top-1/2 transform -translate-y-1/2 translate-x-full hover right-0 bg-primary h-24 px-[3px] flex  items-center rounded-tr-full rounded-br-full"
        >
          <span
            class={`iconfont icon-arrow-right text-white transform ${
              isDisplay.value && "rotate-180"
            } `}
          ></span>
        </div>
      </div>
    );
  },
});
