import { useDataStore } from "@/pages/exam/store/dataStore";
import { Button } from "@arco-design/web-vue";
import { IconLeftCircle, IconRightCircle } from "@arco-design/web-vue/es/icon";
import { defineComponent } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();
    return () => (
      <div class="space-x-3 flex items-center">
        {dataStore.currentIndex > 0 && (
          <Button
            onClick={() => {
              dataStore.changeCurrentToIndex(dataStore.currentIndex - 1);
            }}
            size="large"
            type="primary"
            style={{
              padding: "0 3em",
            }}
            class=" text-[16px] space-x-2"
          >
            <IconLeftCircle class=" text-lg" />
            <span>上一页</span>
          </Button>
        )}

        {dataStore.currentIndex < dataStore.questionList.length - 1 && (
          <Button
            size="large"
            onClick={() => {
              dataStore.changeCurrentToIndex(dataStore.currentIndex + 1);
            }}
            type="primary"
            style={{
              padding: "0 3em",
            }}
            class="text-[16px] space-x-2"
          >
            <span>下一页</span>
            <IconRightCircle class=" text-lg" />
          </Button>
        )}
      </div>
    );
  },
});
