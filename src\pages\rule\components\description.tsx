import { Button } from "@arco-design/web-vue";
import image10 from "@assets/images/image10.jpg";
import image11 from "@assets/images/image11.jpg";
import image12 from "@assets/images/image12.jpg";
import image13 from "@assets/images/image13.jpg";
import image6 from "@assets/images/image6.jpg";
import image7 from "@assets/images/image7.jpg";
import image8 from "@assets/images/image8.jpg";
import image9 from "@assets/images/image9.jpg";
import { findLastIndex, throttle } from "lodash";
import { defineComponent, onMounted, ref } from "vue";
import { JSX } from "vue/jsx-runtime";
export default defineComponent({
  emits: {
    nextEvent: () => null,
    backEvent: () => null,
  },
  setup(_, ctx) {
    const containerEl = ref<Element>();
    const currentIndex = ref(0);
    onMounted(initListening);

    function initListening() {
      containerEl.value?.addEventListener(
        "scroll",
        throttle((e) => {
          // 获取滚动的容器div
          const containerDiv = e.target as HTMLDivElement;
          //   获取滚动的距离
          const scrollTop = containerDiv.scrollTop;
          currentIndex.value = Math.max(
            findLastIndex(Array.from(containerDiv.children), (e) => {
              const item = e as HTMLDivElement;
              return item.offsetTop < scrollTop;
            }),
            0
          );
          1000;
        })
      );
    }

    function scrollTopIndex(index: number) {
      // 获取滚动的容器div
      const containerDiv = containerEl.value?.children[index] as HTMLDivElement;
      containerEl.value?.scrollTo({
        top: containerDiv.offsetTop,
        behavior: "smooth",
      });
    }

    const blockList: {
      id: number;
      title: string;
      content: JSX.Element;
    }[] = [
      {
        id: 1,
        title: "重要事项",
        content: (
          <ul class="list-disc list-inside space-y-5">
            <li>
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              所有考生在<span class=" font-bold">草稿纸</span>
              上书写的所有答题结果<span class=" font-bold">一律无效</span>；
              <span class=" font-bold">草稿纸</span>
              在考试结束后<span class=" font-bold">一律不得</span>带出考场。
            </li>
            <li>
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              在考试资料、试题内容较多的情况下，请务必用鼠标拖动屏幕的
              <span class=" font-bold">滚动条</span>，以避免遗漏重要信息。
            </li>
            <li>
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              主观题的<span class=" font-bold">资料区</span>
              以及作答区的工具栏使用方法可以参考
              <span class=" font-bold">答题辅助功能使用说明</span>
              。此外，为方便考生作答，答题时使用键盘直接输入数字或符号，以及使用快捷键功能，系统都可接受，作答结果不受影响。
            </li>
            <li>
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              在考试过程中，如果遇到任何您认为与机考系统软件、设备硬件故障相关的问题，请立即举手向监考人员示意，并在等待解决的过程中保持安静以免影响其他考生。
            </li>
          </ul>
        ),
      },
      {
        id: 2,
        title: "机考系统布局介绍",
        content: (
          <div class=" space-y-5">
            <div>
              机考系统布局分为四个区域，主要分为
              <span class=" font-bold">标题区、试题列表区、答题区、工具栏区</span>
              ，如下图所示。
            </div>
            <img class="mx-auto w-4/5" src={image6} alt="" />
            <div>
              <span class=" font-bold">标题区</span>，如下图所示。
            </div>
            <img class="mx-auto w-4/5" src={image7} alt="" />
            <ul class="list-disc list-inside space-y-3">
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">字号调节：</span>
                考生可以调节答题界面文字大小，字号有小、中、大三种，默认字号为中。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">作答进度：</span>
                考生可以查看已答试题的题量和总题量。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">剩余时间：</span>
                显示本场考试剩余答题时间。考生应合理分配答题时间。一旦整场考试时间结束，无需考生进行确认，机考系统会自动禁止未交卷的考生继续答题，并自动执行收卷操作，自动提交个人答题结果。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">暂离锁屏：</span>
                如果考生在征得监考人员同意的情况下需要暂时离开考位，为保护个人答题界面的安全，可以点击暂离锁屏按钮，对答题界面进行屏幕保护。关于暂离锁屏功能的详细介绍，请在暂离锁屏功能使用说明进一步了解。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">座位号：</span>
                机考系统为每台考试机预先设定的 <span class=" font-bold">编号</span>。
              </li>
              <div>
                <span class=" font-bold">试题列表区</span>，如下图所示。
              </div>
              <img class="mx-auto w-4/5" src={image8} alt="" />
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">考生信息</span>
                ：此处显示考生入场时拍摄的照片及个人信息。在考试过程中，如考场监考人员、巡考人员需要核对个人信息，请考生予以配合。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">试题列表显示/隐藏</span>
                ：考生可以通过试题列表显示/隐藏功能，将左侧的试题列表面板隐藏，将答题界面最大化显示；试题列表也可由考生自行恢复显示。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">试题列表</span> 中，以
                <span class=" font-bold">数字</span>
                代表题号。考生可以查看试卷结构和题量，以及每道试题的作答状态。
              </li>
              试题的状态用不同的<span class=" font-bold">图标</span>
              样式进行区分，包括当前试题、未完成试题、部分完成试题、已完成试题、标记的试题。
              如考生需要快速的跳转到某道试题，考生可以通过点击
              <span class=" font-bold">试题列表</span>上对应的
              <span class=" font-bold">数字</span>
              标签，系统会直接进入到该试题，方便考生进行答题或检查试题。 当
              <span class=" font-bold">试题</span>较多时，考生可以用鼠标上下拖动
              <span class=" font-bold">纵向滚动条</span>
              以查看全部试题，以免遗漏。
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">交卷</span>
                ：在整场考试时限未到之前，不允许提前交卷。
              </li>
              <div class=" font-bold">答题区</div>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                答题区内将根据试题内容呈现出
                <span class=" font-bold">客观题</span>和<span class=" font-bold">主观题</span>
                两种题型，考生需要在此区域内进行作答。
              </li>
              <div class=" font-bold">工具栏区，如下图所示。</div>
              <img class="mx-auto w-4/5" src={image9} alt="" />
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">标记本题</span>
                ：如果需要提醒自己稍后返回检查当前试题，则可以点击
                <span class=" font-bold">标记本题</span>按钮，再次点击
                <span class=" font-bold">取消标记</span>
                按钮则取消标记。被标记的试题会在左侧的试题列表中突出显示。对试题所作的标记，不会被作为答题结果，也不会影响考生得分情况。合理使用试题标记功能，可以帮助考生在大量的试题中快速查找到需要重点检查的试题。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">上一页</span>：可以通过点击
                <span class=" font-bold">上一页</span>按钮，按顺序进入
                <span class=" font-bold">上一个</span>答题页面。
              </li>
              <li class="pl-6">
                <span class=" inline-block w-1 h-1 rounded-full mr-2 align-middle bg-gray-800"></span>
                <span class=" font-bold">下一页</span>：可以通过点击
                <span class=" font-bold">下一页</span>按钮，按顺序进入
                <span class=" font-bold">下一个</span>答题页面。
              </li>
            </ul>
          </div>
        ),
      },
      // {
      //   id: 3,
      //   title: "答题辅助功能使用说明",
      //   content: (
      //     <div class=" space-y-5">
      //       <div class="pl-6">
      //         机考系统提供了辅助工具功能，协助考生完成答题。
      //       </div>
      //       <ul class="list-disc list-inside space-y-5">
      //         <li class=" text-primary font-bold pl-6">资料区辅助工具</li>
      //         <div class="pl-6">
      //           资料区放大：如考生需要最大化显示资料区，可以通过点击放大按钮，便于考生阅读资料区内容，如考生需要恢复到页面初始大小，可以通过点击恢复按钮。
      //         </div>
      //         <li class=" text-primary font-bold pl-6">
      //           作答区辅助工具，如下图所示。
      //         </li>
      //         <img class="mx-auto w4/5" src={image9} alt="" />
      //       </ul>
      //       <ul class="list-decimal list-inside space-y-5">
      //         <li class="pl-6">
      //           1.用鼠标在答题区选中任意一段文字后，再用鼠标点击复制按钮，可以将选中部分的文字复制到剪切板中，被复制的文字可以被粘贴到答题区内。
      //         </li>
      //         <li class="pl-6">
      //           2.用鼠标在答题区选中已经录入的文字，再用鼠标点击剪切按钮，可以将选中部分的文字剪切到剪切板中，之后可以粘贴到答题区内。
      //         </li>
      //         <div class="pl-6">
      //           使用剪切功能，可以快捷地调整文字在答案中的位置。
      //         </div>
      //         <div class=" text-red-500 pl-6">
      //           注意：资料区的内容不能被复制或剪切。
      //         </div>
      //         <li class="pl-6">
      //           3.用鼠标点击粘贴按钮，可以将已经复制/剪切的内容粘贴到答题区中，被粘贴的文字会插入在光标当前所在位置。
      //         </li>
      //         <div class=" text-red-500 pl-6">
      //           5.注意：资料区内容不能被删除或修改，考生也不能将内容粘贴到资料区中。
      //         </div>
      //         <li class="pl-6">
      //           4.可使用工具栏上的居中、靠左和靠右按钮对已录入的文字进行简单的文字排版。
      //         </li>
      //         <li class="pl-6">5.工具栏右侧的字符数实时统计输入的文字数量。</li>
      //         <li class="pl-6">
      //           6.机考系统上安装了多种常用输入法，考生可以自行选择使用。
      //         </li>
      //         用鼠标点击输入法按钮可以查看可选输入法列表，在列表中用鼠标点击需要使用的输入法即可完成输入法选择和切换。
      //         除使用输入法按钮外，考生还可以通过键盘快捷键组合Ctrl+Shift，在机考系统上选择已安装的输入法。
      //         <li class="pl-6">
      //           7.
      //           作答区放大：如考生需要最大化显示作答区，可以通过点击放大按钮，便于考生录入作答区答案，如考生需要恢复到页面初始大小，可以通过点击恢复按钮
      //         </li>
      //       </ul>
      //     </div>
      //   ),
      // },
      {
        id: 4,
        title: "答题界面介绍",
        content: (
          <ul class="list-disc list-inside space-y-5">
            <div>答题界面按试题类型分为客观题和主观题两种题型，以下分别进行介绍。</div>
            <div class=" text-primary font-bold">客观题</div>
            <div>
              <span class=" font-bold">客观题</span>答题界面如下图所示。
            </div>
            <img class="mx-auto w-4/5" src={image10} alt="" />
            <div>
              <span class=" font-bold">客观题</span>包括
              <span class=" font-bold">单项选择题、多项选择题和判断题。</span>
            </div>
            <div>
              <span class=" font-bold">单项选择题</span>和<span class=" font-bold">判断题</span>
              答题时，考生直接用鼠标点击备选项中认为正确的一个选项。如果需要撤销已经选中的选项，再次点击该选项、或者用鼠标点击其他备选项即可；机考系统只允许考生在所有备选项中选择一个备选项作为答案。
            </div>
            多项选择题答题时，考生直接用鼠标点击备选项中认为正确的所有选项。如果需要撤销已经选中的选项，再次点击该选项即可；机考系统允许考生在所有备选项中选择任意多个备选项作为答案。
            <div class=" text-primary font-bold">主观题</div>
            <div>主观题答题界面如下图所示。</div>
            <img class="mx-auto w-4/5" src={image11} alt="" />
            <div>主观题按界面布局分成资料区和作答区上下两个部分，以下分别进行介绍。</div>
            <li class=" text-primary font-bold">
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              资料区
            </li>
            <div>
              当资料区内容较多时，考生可以用鼠标上下拖动资料区右侧的纵向滚动条以阅读全部内容，以免遗漏。
            </div>
            <li class=" text-primary font-bold">
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              作答区
            </li>
            <div>
              当作答区内容较多时，考生可以用鼠标上下拖动作答区右侧的纵向滚动条以作答全部内容，以免遗漏。
            </div>
            <div>作答时考生应直接将答案录入指定的答题区域中。在草稿纸上书写的所有答案无效。</div>
            <div>在考生答题过程中，机考系统会自动为考生保存答案。</div>
          </ul>
        ),
      },
      {
        id: 5,
        title: "答题辅助功能使用说明",
        content: (
          <ul class="list-disc list-inside space-y-5">
            <div class=" font-bold">机考系统提供了辅助工具功能，协助考生完成答题。</div>
            <div class=" text-primary font-bold">
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              资料区辅助工具
            </div>
            <div>
              <span class=" font-bold">资料区</span>
              放大：如考生需要最大化显示 <span class=" font-bold">资料区</span>
              ，可以通过点击 <span class=" font-bold">放大</span>
              按钮，便于考生阅读<span class=" font-bold">资料区</span>
              内容，如考生需要<span class=" font-bold">恢复</span>
              到页面初始大小，可以通过点击<span class=" font-bold">恢复</span>
              按钮
            </div>
            <div class=" text-primary font-bold">
              <span class=" inline-block w-1 h-1 rounded-full bg-primary mr-2 align-middle"></span>
              作答区辅助工具，如下图所示。
            </div>
            <img class="mx-auto w-4/5" src={image12} alt="" />
            <ul class="list-decimal list-inside space-y-3">
              <li>
                1.用鼠标在答题区选中任意一段文字后，再用鼠标点击
                <span class=" font-bold">复制</span>
                按钮，可以将选中部分的文字复制到剪切板中，被复制的文字可以被粘贴到答题区内。
              </li>
              <li>
                2.用鼠标在答题区选中已经录入的文字，再用鼠标点击
                <span class=" font-bold">剪切</span>
                按钮，可以将选中部分的文字剪切到
                <span class=" font-bold">剪切</span>
                板中，之后可以粘贴到答题区内。
              </li>
              <div>使用剪切功能，可以快捷地调整文字在答案中的位置。</div>
              <div class=" text-red-500">注意：资料区的内容不能被复制或剪切。</div>
              <li>
                3.用鼠标点击<span class=" font-bold">粘贴</span>
                按钮，可以将已经复制/剪切的内容粘贴到答题区中，被粘贴的文字会插入在光标当前所在位置。
              </li>
              <div class=" text-red-500">
                注意：资料区内容不能被删除或修改，考生也不能将内容粘贴到资料区中。
              </div>
              <li>
                4.可使用工具栏上的<span class=" font-bold">居中</span>、
                <span class=" font-bold">靠左</span>和<span class=" font-bold">靠右</span>
                按钮对已录入的文字进行简单的文字排版。
              </li>
              <li>
                5.工具栏右侧的<span class=" font-bold">字符数</span>
                实时统计输入的文字数量。
              </li>
              <li>
                6.机考系统上安装了多种常用<span class=" font-bold">输入法</span>
                ，考生可以自行选择使用。
              </li>
              用鼠标点击<span class=" font-bold">输入法</span>按钮可以查看可选
              <span class=" font-bold">输入法</span>
              列表，在列表中用鼠标点击需要使用的
              <span class=" font-bold">输入法</span>即可完成
              <span class=" font-bold">输入法</span>选择和切换。 除使用
              <span class=" font-bold">输入法</span>
              按钮外，考生还可以通过键盘快捷键组合
              <span class=" font-bold">Ctrl+Shift</span>
              ，在机考系统上选择已安装的
              <span class=" font-bold">输入法</span>。
              <li>
                7.<span class=" font-bold">作答区</span>
                放大：如考生需要最大化显示<span class=" font-bold">作答区</span>
                ，可以通过点击<span class=" font-bold">放大</span>
                按钮，便于考生录入
                <span class=" font-bold">作答区</span>
                答案，如考生需要<span class=" font-bold">恢复</span>
                到页面初始大小，可以通过点击恢复按钮。
              </li>
            </ul>
          </ul>
        ),
      },
      {
        id: 6,
        title: "暂离锁屏功能使用说明",
        content: (
          <ul class="list-disc list-inside space-y-5">
            <div class="pl-6">
              如果考生使用了<span class=" font-bold">暂离锁屏</span>
              功能，答题界面将处于<span class=" font-bold">屏幕保护</span>
              状态，如下图所示。
            </div>
            <img class="mx-auto w-4/5" src={image13} alt="" />
            <li class="pl-6">
              如果考生的答题界面处于<span class=" font-bold">暂离锁屏</span>
              状态时，请考生注意<span class=" font-bold">暂离锁屏</span>
              界面上的剩余时间，此时考试机同样
              <span class=" font-bold">计时</span>
              ，一旦整场考试结束，无需考生进行
              <span class=" font-bold">解除暂离锁屏</span>
              确认，机考系统会自动禁止<span class=" font-bold">暂离锁屏</span>
              的考生继续答题，并自动执行<span class=" font-bold">收卷</span>
              操作，自动提交个人答题结果。
            </li>
            {/* <li class="pl-6">
              如果考生在<span class=" font-bold">暂离锁屏</span>
              期间，需要返回考试，考生在“
              <span class=" font-bold">请输入身份证号</span>
              ”编辑框中输入身份证号，然后点击
              <span class=" font-bold">继续考试</span>按钮。
            </li> */}
          </ul>
        ),
      },
      {
        id: 7,
        title: "结束考试",
        content: (
          <ul class="list-disc list-inside space-y-5">
            <div class=" font-bold text-primary pl-6">考生提前交卷</div>
            <div class="pl-6">在整场考试时限未到之前，不允许提前交卷。</div>
            <div class=" font-bold text-primary pl-6">整场考试结束</div>
            <div class="pl-6">
              一旦整场考试时间结束，机考系统会自动禁止所有未交卷的考生继续答题，并为未交卷的考生
              <span class=" font-bold">统一交卷</span>
              。此时，无需考生对交卷操作进行确认。
            </div>
            <div class="pl-6">
              注意：在正常考试结束、系统自动收卷时，如有考生处于
              <span class=" font-bold">暂离锁屏</span>
              状态，机考系统也会自动为其进行交卷操作。考生返回后，
              <span class=" font-bold">不能</span>再次登录机考系统。
            </div>
            <div class="pl-6">
              考试结束后，机考系统会<span class=" font-bold">自动</span>
              收集并上传所有考生的<span class=" font-bold">答题结果</span>。
            </div>
          </ul>
        ),
      },
    ];
    return () => (
      <div class=" w-full h-full flex flex-col py-3 px-20 space-y-5">
        <div class=" text-center  font-bold" style="font-size:2rem">
          操作说明
        </div>
        <div class=" text-center font-bold text-sm">非官方系统，仅用于模拟</div>
        <div class=" flex-1 bg-white rounded-xl drop-shadow-lg flex flex-col  divide-y">
          <div class="flex-1 flex p-6 space-x-5">
            <div class=" flex-1 relative">
              <BuildLeft />
            </div>
            <BuildRight />
          </div>
          <div class=" p-5 flex justify-center space-x-20">
            <Button onClick={() => ctx.emit("backEvent")} class=" px-10" size="large">
              返回规则
            </Button>
            <Button
              onClick={() => ctx.emit("nextEvent")}
              type="primary"
              class=" px-10"
              size="large"
            >
              开始考试
            </Button>
          </div>
        </div>
      </div>
    );

    function BuildLeft() {
      return (
        <div
          ref={containerEl}
          class=" absolute overflow-y-scroll w-full h-full space-y-10 text-base"
        >
          <div class=" space-y-3 font-bold text-sm">
            <div>本说明详细介绍了机考系统的使用方法、答题方式，以及考生需要了解的重要事项。</div>
            <div>在开始考试之前，请仔细阅读。</div>
          </div>
          {blockList.map((e) => {
            return (
              <div key={e.id} class=" space-y-4 text-sm">
                <div class=" font-bold text-primary text-base">{e.title}</div>
                {e.content}
              </div>
            );
          })}
        </div>
      );
    }

    function BuildRight() {
      return (
        <div class="  w-64 relative h-fit">
          <div class=" absolute left-[14px] w-1 bg-gray-100 h-full"></div>
          <div class=" border-4 w-5 h-5 rounded-full bg-white left-[6px] absolute"></div>
          <div class=" border-4 w-5 h-5 bottom-0 rounded-full bg-white left-[6px] absolute"></div>
          <div class="space-y-3 py-10">
            {blockList.map((e, i) => {
              const isCurrent = currentIndex.value === i;
              return (
                <div
                  onClick={() => {
                    scrollTopIndex(i + 1);
                  }}
                  key={e.id}
                  class="flex items-center space-x-3 hover:cursor-pointer  "
                >
                  <div class=" w-8 h-8 relative">
                    <div
                      class={`absolute left-1/2 top-1/2 transition-all -translate-x-1/2 rounded-full -translate-y-1/2  ${
                        isCurrent ? " w-5 h-5 bg-primary" : " bg-gray-200 w-4 h-4 "
                      }`}
                    ></div>
                  </div>
                  <div class={`text-base font-bold  ${isCurrent && "text-primary"}`}>{e.title}</div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
  },
});
