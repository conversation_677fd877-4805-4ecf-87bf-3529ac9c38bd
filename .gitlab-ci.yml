stages:
  - build
  - deploy-to-test
  - deploy-to-prod

variables:
  SERVICE_TYPE: http-vue                                                # 服务类型: [http-swoft|rpc-swoft|http-fpm|http-vue|cli-swoft|http-html]
  NAMESPACE: training                                                   # k8s命名空间
  SERVICE_PORT: 80                                                      # 服务内部端口号
  URI_PREFIX: "/simexam/jqxd"                                               # URI路径前缀,跟路径留空
  ENABLE_JINBIAOCHI_DOMAIN: "false"                                     # 启用jinbiaochi内网域名
  #----------------测试环境-------------------------------------------------------------------------------------------------------
  #注意：多域名下如果有域名启用https，则主域名必须开启https
  TEST_DOMAIN_SKB: "www.test.shikaobang.cn"                             # 事考帮公网域名
  TEST_DOMAIN_ALIAS_SKB: ""                                             # 事考帮公网域名别名配置,与主域名共用ssl证书(如果有)
  TEST_DOMAIN_ENABLE_HTTPS_SKB: "true"                                  # 事考帮公网是否启用https
  TEST_HTTPS_SECRET_NAME_SKB: "test.shikaobang.cn"                      # 事考帮公网https证书secret名称
  TEST_DOMAIN_JBCGK: "www.test.jbcgk.com"                               # 金标尺公考公网域名
  TEST_DOMAIN_ALIAS_JBCGK: ""                                           # 金标尺公考公网域名别名配置,与主域名共用ssl证书(如果有)
  TEST_DOMAIN_ENABLE_HTTPS_JBCGK: "true"                                # 金标尺公考公网是否启用https
  TEST_HTTPS_SECRET_NAME_JBCGK: "test.jbcgk.com"                        # 金标尺公考公网https证书secret名称
  TEST_DOMAIN_GPJS: "www.test.guopeichina.com"                          # 国培教师公网域名
  TEST_DOMAIN_ALIAS_GPJS: ""                                            # 国培教师公网域名别名配置,与主域名共用ssl证书(如果有)
  TEST_DOMAIN_ENABLE_HTTPS_GPJS: "true"                                 # 国培教师公网是否启用https
  TEST_HTTPS_SECRET_NAME_GPJS: "test.guopeichina.com"                   # 国培教师公网https证书secret名称
  TEST_DOMAIN1_GPJS: "www.test.jbcjiaoshi.com"                          # 国培教师公网副域名1
  TEST_DOMAIN1_ENABLE_HTTPS_GPJS: "true"                                # 国培教师公网副域名1是否启用https
  TEST_DOMAIN1_HTTPS_SECRET_NAME_GPJS: "test.jbcjiaoshi.com"            # 国培教师公网副域名1https证书secret名称
  TEST_DOMAIN_JBCZSB: "www.test.jbczsb.com"                             # 金标尺专升本公网域名
  TEST_DOMAIN_ALIAS_JBCZSB: ""                                          # 金标尺专升公网域名别名配置,与主域名共用ssl证书(如果有)
  TEST_DOMAIN_ENABLE_HTTPS_JBCZSB: "true"                               # 金标尺专升本公网域名是否启用https
  TEST_DOMAIN_HTTPS_SECRET_NAME_JBCZSB: "test.jbczsb.com"               # 金标尺专升本公网域名https证书secret名称
  TEST_DOMAIN_XDB: "www.test.xuandiaobang.com"                          # 选调帮公网域名
  TEST_DOMAIN_ALIAS_XDB: ""                                             # 选调帮网域名别名配置,与主域名共用ssl证书(如果有)
  TEST_DOMAIN_ENABLE_HTTPS_XDB: "true"                                  # 选调帮公网域名是否启用https
  TEST_DOMAIN_HTTPS_SECRET_NAME_XDB: "test.xuandiaobang.com"            # 选调帮公网域名https证书secret名称
  TEST_REQUEST_CPU: 10m                                                 # CPU需求
  TEST_REQUEST_MEM: 30Mi                                                # 内存需求
  TEST_LIMIT_CPU: 1000m                                                 # CPU使用上限
  TEST_LIMIT_MEM: 2Gi                                                   # 内存使用上限
  TEST_REPLICAS: 1                                                      # 副本数
  TEST_INGRESS_CLASS: nginx-external                                    # 使用哪一个nginx-ingress-controller作为公网流量入口,内部服务忽略
  #-----------------生产环境------------------------------------------------------------------------------------------------------
  #注意：多域名下如果有域名启用https，则主域名必须开启https
  PROD_DOMAIN_SKB: "www.shikaobang.cn"                                  # 事考帮公网域名
  PROD_DOMAIN_ALIAS_SKB: ""                                             # 事考帮公网域名别名配置,与主域名共用ssl证书(如果有)
  PROD_DOMAIN_ENABLE_HTTPS_SKB: "true"                                  # 事考帮公网是否启用https
  PROD_HTTPS_SECRET_NAME_SKB: "shikaobang.cn"                           # 事考帮公网https证书secret名称
  PROD_DOMAIN_JBCGK: "www.jbcgk.com"                                    # 金标尺公考公网域名
  PROD_DOMAIN_ALIAS_JBCGK: ""                                           # 金标尺公考公网域名别名配置,与主域名共用ssl证书(如果有)
  PROD_DOMAIN_ENABLE_HTTPS_JBCGK: "true"                                # 金标尺公考公网是否启用https
  PROD_HTTPS_SECRET_NAME_JBCGK: "jbcgk.com"                             # 金标尺公考公网https证书secret名称
  PROD_DOMAIN_GPJS: "www.guopeichina.com"                               # 国培教师公网域名
  PROD_DOMAIN_ALIAS_GPJS: ""                                            # 国培教师公网域名别名配置,与主域名共用ssl证书(如果有)
  PROD_DOMAIN_ENABLE_HTTPS_GPJS: "true"                                 # 国培教师公网是否启用https
  PROD_HTTPS_SECRET_NAME_GPJS: "guopeichina.com"                        # 国培教师公网https证书secret名称
  PROD_DOMAIN1_GPJS: "www.jbcjiaoshi.com"                               # 国培教师公网副域名1
  PROD_DOMAIN1_ENABLE_HTTPS_GPJS: "true"                                # 国培教师公网副域名1是否启用https
  PROD_DOMAIN1_HTTPS_SECRET_NAME_GPJS: "jbcjiaoshi.com"                 # 国培教师公网副域名1https证书secret名称
  PROD_DOMAIN_JBCZSB: "www.jbczsb.com"                                  # 金标尺专升本公网域名
  PROD_DOMAIN_ALIAS_JBCZSB: ""                                          # 金标尺专升公网域名别名配置,与主域名共用ssl证书(如果有)
  PROD_DOMAIN_ENABLE_HTTPS_JBCZSB: "true"                               # 金标尺专升本公网域名是否启用https
  PROD_DOMAIN_HTTPS_SECRET_NAME_JBCZSB: "jbczsb.com"                    # 金标尺专升本公网域名https证书secret名称
  PROD_DOMAIN_XDB: "www.xuandiaobang.com"                               # 金标尺专升本公网域名
  PROD_DOMAIN_ALIAS_XDB: ""                                             # 金标尺专升公网域名别名配置,与主域名共用ssl证书(如果有)
  PROD_DOMAIN_ENABLE_HTTPS_XDB: "true"                                  # 金标尺专升本公网域名是否启用https
  PROD_DOMAIN_HTTPS_SECRET_NAME_XDB: "xuandiaobang.com"                 # 金标尺专升本公网域名https证书secret名称
  PROD_REQUEST_CPU: 50m                                                # CPU需求
  PROD_REQUEST_MEM: 50Mi                                               # 内存需求
  PROD_LIMIT_CPU: 100m                                                 # CPU使用上限
  PROD_LIMIT_MEM: 100Mi                                                   # 内存使用上限
  PROD_REPLICAS: 1                                                      # 副本数
  PROD_INGRESS_CLASS: nginx-external-training                           # 使用哪一个nginx-ingress-controller作为公网流量入口,内部服务忽略

# 打包docker镜像并上传到腾讯镜像仓库
build:
  stage: build
  script:
    - |
      if [ "${SERVICE_TYPE}" == "http-vue" ]; then
          sudo docker build -t ccr.ccs.tencentyun.com/tiku/${DEPLOYMENT_NAME}:${CI_COMMIT_SHORT_SHA} --build-arg pro=${PRO} --build-arg uri_prefix=${URI_PREFIX} -f ./k8s/Dockerfile-${SERVICE_TYPE} .
      fi
      sudo docker push ccr.ccs.tencentyun.com/tiku/${DEPLOYMENT_NAME}:${CI_COMMIT_SHORT_SHA}
  tags:
    - default
  only:
    refs:
      - master
      - /^issue-.*/
    variables:
      - $CI_COMMIT_TITLE =~ /^build:(?:skb|jbcgk|gpjs|jbczsb|xuandiao).*$/

# 部署到测试环境
deploy-to-test:
  stage: deploy-to-test
  script:
    - deloy
  tags:
    - default
  variables:
    APP_ENV: test
  environment:
    name: test
  only:
    refs:
      - master
      - /^issue-.*/
    variables:
      - $CI_COMMIT_TITLE =~ /^build:(?:skb|jbcgk|gpjs|jbczsb|xuandiao).*$/

# 部署到生产环境
deploy-to-prod:
  stage: deploy-to-prod
  script:
    - auth
    - deloy
  tags:
    - default
  variables:
    APP_ENV: prod
  environment:
    name: prod
  when: manual
  only:
    refs:
      - master
      - /^issue-.*/
    variables:
      - $CI_COMMIT_TITLE =~ /^build:(?:skb|jbcgk|gpjs|jbczsb|xuandiao).*$/

# 部署函数定义
.devops: &devops |
  set +e
  export URI_PREFIX_S=${URI_PREFIX:1}
  [[ ${CI_COMMIT_TITLE} =~ ^build:(skb|jbcgk|gpjs|jbczsb|xuandiao).* ]]
  export PRO=${BASH_REMATCH[1]}
  export DEPLOYMENT_NAME=${CI_PROJECT_NAME}-$PRO
  if [ "${APP_ENV}" == "test" ]; then
    cat ${KUBE_CONFIG_TEST} >> ./kubeconfig
    export REPLICAS=${TEST_REPLICAS}
    export REQUEST_CPU=${TEST_REQUEST_CPU}
    export REQUEST_MEM=${TEST_REQUEST_MEM}
    export LIMIT_CPU=${TEST_LIMIT_CPU}
    export LIMIT_MEM=${TEST_LIMIT_MEM}
    case "$PRO" in 
    "skb")
      export DOMAIN=${TEST_DOMAIN_SKB}
      export DOMAIN_ALIAS=${TEST_DOMAIN_ALIAS_SKB}
      export DOMAIN_ENABLE_HTTPS=${TEST_DOMAIN_ENABLE_HTTPS_SKB}
      export HTTPS_SECRET_NAME=${TEST_HTTPS_SECRET_NAME_SKB}
      ;;
    "jbcgk")
      export DOMAIN=${TEST_DOMAIN_JBCGK}
      export DOMAIN_ALIAS=${TEST_DOMAIN_ALIAS_JBCGK}
      export DOMAIN_ENABLE_HTTPS=${TEST_DOMAIN_ENABLE_HTTPS_JBCGK}
      export HTTPS_SECRET_NAME=${TEST_HTTPS_SECRET_NAME_JBCGK}
      ;;
    "gpjs")
      export DOMAIN=${TEST_DOMAIN_GPJS}
      export DOMAIN_ALIAS=${TEST_DOMAIN_ALIAS_GPJS}
      export DOMAIN_ENABLE_HTTPS=${TEST_DOMAIN_ENABLE_HTTPS_GPJS}
      export HTTPS_SECRET_NAME=${TEST_HTTPS_SECRET_NAME_GPJS}
      export DOMAIN1=${TEST_DOMAIN1_GPJS}
      export DOMAIN1_ENABLE_HTTPS=${TEST_DOMAIN1_ENABLE_HTTPS_GPJS}
      export DOMAIN1_HTTPS_SECRET_NAME=${TEST_DOMAIN1_HTTPS_SECRET_NAME_GPJS}
      ;;
    "jbczsb")
      export DOMAIN=${TEST_DOMAIN_JBCZSB}
      export DOMAIN_ALIAS=${TEST_DOMAIN_ALIAS_JBCZSB}
      export DOMAIN_ENABLE_HTTPS=${TEST_DOMAIN_ENABLE_HTTPS_JBCZSB}
      export HTTPS_SECRET_NAME=${TEST_HTTPS_SECRET_NAME_JBCZSB}
      ;;
    "xuandiao")
      export DOMAIN=${TEST_DOMAIN_XDB}
      export DOMAIN_ALIAS=${TEST_DOMAIN_ALIAS_XDB}
      export DOMAIN_ENABLE_HTTPS=${TEST_DOMAIN_ENABLE_HTTPS_XDB}
      export HTTPS_SECRET_NAME=${TEST_HTTPS_SECRET_NAME_XDB}
    esac
    export INGRESS_CLASS=${TEST_INGRESS_CLASS}
    export INTERNAL_DOMAIN="${DEPLOYMENT_NAME}.${NAMESPACE}.test-tke.shikaobang.cn"
  elif [ "${APP_ENV}" == "prod" ]; then
    cat ${KUBE_CONFIG_PROD} >> ./kubeconfig
    export REPLICAS=${PROD_REPLICAS}
    export REQUEST_CPU=${PROD_REQUEST_CPU}
    export REQUEST_MEM=${PROD_REQUEST_MEM}
    export LIMIT_CPU=${PROD_LIMIT_CPU}
    export LIMIT_MEM=${PROD_LIMIT_MEM}
    case "$PRO" in 
    "skb")
      export DOMAIN=${PROD_DOMAIN_SKB}
      export DOMAIN_ALIAS=${PROD_DOMAIN_ALIAS_SKB}
      export DOMAIN_ENABLE_HTTPS=${PROD_DOMAIN_ENABLE_HTTPS_SKB}
      export HTTPS_SECRET_NAME=${PROD_HTTPS_SECRET_NAME_SKB}
      ;;
    "jbcgk")
      export DOMAIN=${PROD_DOMAIN_JBCGK}
      export DOMAIN_ALIAS=${PROD_DOMAIN_ALIAS_JBCGK}
      export DOMAIN_ENABLE_HTTPS=${PROD_DOMAIN_ENABLE_HTTPS_JBCGK}
      export HTTPS_SECRET_NAME=${PROD_HTTPS_SECRET_NAME_JBCGK}
      ;;
    "gpjs")
      export DOMAIN=${PROD_DOMAIN_GPJS}
      export DOMAIN_ALIAS=${PROD_DOMAIN_ALIAS_GPJS}
      export DOMAIN_ENABLE_HTTPS=${PROD_DOMAIN_ENABLE_HTTPS_GPJS}
      export HTTPS_SECRET_NAME=${PROD_HTTPS_SECRET_NAME_GPJS}
      export DOMAIN1=${PROD_DOMAIN1_GPJS}
      export DOMAIN1_ENABLE_HTTPS=${PROD_DOMAIN1_ENABLE_HTTPS_GPJS}
      export DOMAIN1_HTTPS_SECRET_NAME=${PROD_DOMAIN1_HTTPS_SECRET_NAME_GPJS}
      ;;
    "jbczsb")
      export DOMAIN=${PROD_DOMAIN_JBCZSB}
      export DOMAIN_ALIAS=${PROD_DOMAIN_ALIAS_JBCZSB}
      export DOMAIN_ENABLE_HTTPS=${PROD_DOMAIN_ENABLE_HTTPS_JBCZSB}
      export HTTPS_SECRET_NAME=${PROD_HTTPS_SECRET_NAME_JBCZSB}
      ;;
    "xuandiao")
      export DOMAIN=${PROD_DOMAIN_XDB}
      export DOMAIN_ALIAS=${PROD_DOMAIN_ALIAS_XDB}
      export DOMAIN_ENABLE_HTTPS=${PROD_DOMAIN_ENABLE_HTTPS_XDB}
      export HTTPS_SECRET_NAME=${PROD_HTTPS_SECRET_NAME_XDB}
    esac
    export INGRESS_CLASS=${PROD_INGRESS_CLASS}
    export INTERNAL_DOMAIN="${DEPLOYMENT_NAME}.${NAMESPACE}.prod-tke.shikaobang.cn"
  fi
  export KUBE_CMD="sudo kubectl --kubeconfig ./kubeconfig -n ${NAMESPACE}"

  function deploy-http-swoft()
  {
    echo "更新/部署Deployment------------------"
    applyDeployment
    echo "更新/部署Service---------------------"
    applyService
    echo "更新/部署Ingress---------------------"
    applyIngress
    echo "部署/更新完成"
  }

  function deploy-rpc-swoft()
  {
    echo "更新/部署Deployment------------------"
    applyDeployment
    echo "更新/部署Service---------------------"
    applyService
    echo "更新/部署Ingress---------------------"
  }

  function deploy-cli-swoft()
  {
    echo "更新/部署Deployment------------------"
    applyDeployment
    echo "部署/更新完成"
  }

  function deploy-http-fpm() 
  {
    echo "更新/部署Deployment------------------"
    applyDeployment
    echo "更新/部署Service---------------------"
    applyService
    echo "更新/部署Ingress---------------------"
    applyIngress
    echo "部署/更新完成"
  }

  function deploy-http-vue()
  {
    echo "更新/部署Deployment------------------"
    applyDeployment
    echo "更新/部署Service---------------------"
    applyService
    echo "更新/部署Ingress---------------------"
    applyIngress
    echo "部署/更新完成"
  }

  function deploy-http-html()
  {
    echo "更新/部署Deployment------------------"
    applyDeployment
    echo "更新/部署Service---------------------"
    applyService
    echo "更新/部署Ingress---------------------"
    applyIngress
    echo "部署/更新完成"
  }

  function auth()
  {
    MEMBERS=`curl -s --header "PRIVATE-TOKEN: ${ACCESS_KEY}" ${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/members/all`
    let len=`echo $MEMBERS | jq '. | length'`
    for((i=0;i<$len;i++));
    do
      user="`echo $MEMBERS | jq .[$i].name`"
      let access=`echo $MEMBERS | jq .[$i].access_level`
      if [ "$user" == "\"${GITLAB_USER_NAME}\"" -a $access -lt 40 ]
      then
        echo "你没有权限执行该操作，至少需要Maintainer权限!"
        exit 1
      fi
    done
  }

  function deloy()
  {
    case $SERVICE_TYPE in
    "http-swoft")
      deploy-http-swoft
      ;;
    "rpc-swoft")
      deploy-rpc-swoft
      ;;
    "http-fpm")
      deploy-http-fpm
      ;;
    "http-vue")
      deploy-http-vue
      ;;
    "http-html")
      deploy-http-html
      ;;
    "cli-swoft")
      deploy-cli-swoft
      ;;
    esac
  }

  # Deployment对象部署
  function applyDeployment()
  {
    envsubst < ./k8s/Deployment-${SERVICE_TYPE}.yaml.template | ${KUBE_CMD} apply -f -
    ${KUBE_CMD} rollout status deployment/${DEPLOYMENT_NAME} --watch --timeout=60s
    if [ $? -ne 0 ]; then
      echo -e "该部署在60秒内未能完成\n可能的原因：系统资源不足或服务健康检查未通过。"
      echo  "Deployment信息:"
      ${KUBE_CMD} describe deployment ${DEPLOYMENT_NAME}
      echo  "Pod信息："
      ${KUBE_CMD} get pod -l app=${DEPLOYMENT_NAME}
      POD_ID=`${KUBE_CMD} get pod -l app=${DEPLOYMENT_NAME} | grep -v NAME | sed -n '/0\/1/p' | head -n 1 | awk '{print $1}'`
      if [ "${POD_ID}" != "" ]; then
        echo "${POD_ID}详情："
        ${KUBE_CMD} describe pod ${POD_ID}
        echo "Pod应用日志："
        ${KUBE_CMD} logs ${POD_ID} -c ${DEPLOYMENT_NAME}
      fi
      echo  "撤销该次部署/更新，执行回滚操作"
      ${KUBE_CMD} rollout undo deployment/${DEPLOYMENT_NAME}
      exit 1
    fi
  }

  # Service对象部署
  function applyService()
  {
    envsubst < ./k8s/Service.yaml.template | ${KUBE_CMD} apply -f -
    if [ $? -ne 0 ]; then 
      echo "Service部署失败，请检查yaml定义！" 
       exit 1
    fi
  }

  # Ingress对象部署
  function applyIngress()
  {
    envsubst < ./k8s/Ingress-HTTP-internal.yaml.template | ${KUBE_CMD} apply -f -
    if [ $? -ne 0 ]; then echo "Ingress-internal部署失败，请检查yaml定义！" && exit 1; fi
    UUID=$(cat /proc/sys/kernel/random/uuid | tr -d '-')
    echo "UUID: $UUID"
    if [ "${DOMAIN_ENABLE_HTTPS}" == "false" ]; then
      cp -f ./k8s/Ingress-HTTP.yaml.template ./k8s/Ingress-$UUID.yaml.template
    elif [ "${DOMAIN_ENABLE_HTTPS}" == "true" ]; then
      cp -f ./k8s/Ingress-HTTPS.yaml.template ./k8s/Ingress-$UUID.yaml.template
    fi
    if [ "${DOMAIN_ALIAS}" != "" ]; then
      sed -i '/annotations:/a\    nginx.ingress.kubernetes.io\/server-alias: \${DOMAIN_ALIAS}' ./k8s/Ingress-$UUID.yaml.template
    fi
    if [ "${DOMAIN}" != "" ]; then
      RULE_BLOCK=$(cat ./k8s/Ingress-$UUID.yaml.template | grep -A350 rules: | grep -v rules )
      if [ "${DOMAIN1}" != "" ]; then
        DOMAIN1_RULE_BLOCK=$(echo "${RULE_BLOCK}" | sed  "s#DOMAIN#DOMAIN1#g")
        echo "${DOMAIN1_RULE_BLOCK}">>./k8s/Ingress-$UUID.yaml.template
        if [ "${DOMAIN1_ENABLE_HTTPS}" == "true" ]; then
          sed -i '/tls:/a\ \ - hosts:\n    - ${DOMAIN1}\n    secretName: \"${DOMAIN1_HTTPS_SECRET_NAME}\"' ./k8s/Ingress-$UUID.yaml.template
        fi
      fi
      if [ "${DOMAIN2}" != "" ]; then
        DOMAIN2_RULE_BLOCK=$(echo "${RULE_BLOCK}" | sed  "s#DOMAIN#DOMAIN2#g")
        echo "${DOMAIN2_RULE_BLOCK}">>./k8s/Ingress-$UUID.yaml.template
        if [ "${DOMAIN2_ENABLE_HTTPS}" == "true" ]; then
          sed -i '/tls:/a\ \ - hosts:\n    - ${DOMAIN2}\n    secretName: \"${DOMAIN2_HTTPS_SECRET_NAME}\"' ./k8s/Ingress-$UUID.yaml.template
        fi
      fi
      if [ "${DOMAIN3}" != "" ]; then
        DOMAIN3_RULE_BLOCK=$(echo "${RULE_BLOCK}" | sed  "s#DOMAIN#DOMAIN3#g")
        echo "${DOMAIN3_RULE_BLOCK}">>./k8s/Ingress-$UUID.yaml.template
        if [ "${DOMAIN3_ENABLE_HTTPS}" == "true" ]; then
          sed -i '/tls:/a\ \ - hosts:\n    - ${DOMAIN3}\n    secretName: \"${DOMAIN3_HTTPS_SECRET_NAME}\"' ./k8s/Ingress-$UUID.yaml.template
        fi
      fi
      if [ "${DOMAIN4}" != "" ]; then
        DOMAIN4_RULE_BLOCK=$(echo "${RULE_BLOCK}" | sed  "s#DOMAIN#DOMAIN4#g")
        echo "${DOMAIN4_RULE_BLOCK}">>./k8s/Ingress-$UUID.yaml.template
        if [ "${DOMAIN4_ENABLE_HTTPS}" == "true" ]; then
          sed -i '/tls:/a\ \ - hosts:\n    - ${DOMAIN4}\n    secretName: \"${DOMAIN4_HTTPS_SECRET_NAME}\"' ./k8s/Ingress-$UUID.yaml.template
        fi
      fi
      if [ "${DOMAIN5}" != "" ]; then
        DOMAIN5_RULE_BLOCK=$(echo "${RULE_BLOCK}" | sed  "s#DOMAIN#DOMAIN5#g")
        echo "${DOMAIN5_RULE_BLOCK}">>./k8s/Ingress-$UUID.yaml.template
        if [ "${DOMAIN5_ENABLE_HTTPS}" == "true" ]; then
          sed -i '/tls:/a\ \ - hosts:\n    - ${DOMAIN5}\n    secretName: \"${DOMAIN5_HTTPS_SECRET_NAME}\"' ./k8s/Ingress-$UUID.yaml.template
        fi
      fi
      if [ "${DOMAIN6}" != "" ]; then
        DOMAIN6_RULE_BLOCK=$(echo "${RULE_BLOCK}" | sed  "s#DOMAIN#DOMAIN6#g")
        echo "${DOMAIN6_RULE_BLOCK}">>./k8s/Ingress-$UUID.yaml.template
        if [ "${DOMAIN6_ENABLE_HTTPS}" == "true" ]; then
          sed -i '/tls:/a\ \ - hosts:\n    - ${DOMAIN6}\n    secretName: \"${DOMAIN6_HTTPS_SECRET_NAME}\"' ./k8s/Ingress-$UUID.yaml.template
        fi
      fi
      envsubst < ./k8s/Ingress-$UUID.yaml.template | ${KUBE_CMD} apply -f -
      if [ $? -ne 0 ]; then echo "Ingress部署失败，请检查yaml定义！" && exit 1; fi
    fi
  }

before_script:
  - *devops



