import { useDataStore } from "@/pages/exam/store/dataStore";
import { IconDownCircle } from "@arco-design/web-vue/es/icon";
import image32 from "@assets/images/image32.png";
import { defineComponent, ref } from "vue";

export default defineComponent({
  setup() {
    // 是否展开
    const isOpen = ref(true);

    // 状态
    const dataStore = useDataStore();

    return () => (
      <div class=" bg-gray-50  px-3 py-5 rounded flex flex-col space-y-3 border-2 border-dashed ">
        <BuildAvatar />
        {isOpen.value && <BuildInfo />}
      </div>
    );

    // 头像
    function BuildAvatar() {
      return (
        <div
          class="w-full flex justify-center "
          onClick={() => {
            isOpen.value = !isOpen.value;
          }}
        >
          <div class=" w-20 h-24   bg-red-200 flex justify-center items-center relative">
            <img src={image32} class=" w-full h-full object-cover rounded" />
            <div
              class={`absolute transform top-1/2 -right-7 -translate-y-1/2 ${
                isOpen.value ? "rotate-180" : "rotate-0"
              } `}
              style="cursor: pointer;"
            >
              <IconDownCircle />
            </div>
          </div>
        </div>
      );
    }

    // 用户资料
    function BuildInfo() {
      return (
        <div class=" space-y-2">
          <div>
            <span style="color: #9CA3AF;">姓&emsp;&emsp;名：</span>
            <span>{dataStore.questionSource?.name}</span>
          </div>
          {/*<div>*/}
          {/*  <span>性&emsp;&emsp;别：</span>*/}
          {/*  <span>{dataStore.questionSource?.sex}</span>*/}
          {/*</div>*/}
          {/* <div>
            <span>身份证号：</span>
            <span>{dataStore.questionSource?.prove}</span>
          </div> */}
          <div>
            <span style="color: #9CA3AF;">考试名称：</span>
            <span>{dataStore.questionSource?.exam}</span>
          </div>
        </div>
      );
    }
  },
});
