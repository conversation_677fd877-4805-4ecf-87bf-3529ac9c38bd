import { Modal, ModalReturn, Notification } from "@arco-design/web-vue";
import { ServicePostOptions, ServiceResponse } from "./types";
import axios, { AxiosInstance, AxiosRequestConfig, HttpStatusCode, Method } from "axios";
import Cookies from "js-cookie";
import { reactive } from "vue";
const state = reactive({
  token: Cookies.get("web_user_token") || "", // 初始化为 cookie 的值，或一个空字符串
});

// 每次登录成功后调用该方法来更新 token
export function updateToken(token: any) {
  state.token = token;
}

// const token = document.cookie.replace(/(?:(?:^|.*;\s*)token\s*\=\s*([^;]*).*$)|^.*$/, "$1");
// const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1Y2lkIjoxNzYxMTE2LCJicmFuZCI6ImpiY2drIiwiYXBwbGljYXRpb24iOiJ3ZWIiLCJ0ZXN0ZXIiOjEsImlzX3RvdXJpc3QiOjAsImlhdCI6MTcyNjU2MzMwNSwiaXNzIjoidWNlbnRlciJ9.lhU-SyDXQI0q65m9akQpOrx5aDTY7dFncXApJ4V1hQM"
class AxiosRequest {
  private instance: AxiosInstance;

  constructor(config: AxiosRequestConfig) {
    this.instance = axios.create(config);
  }

  private async send<R, P = any>(
    url: string,
    option: {
      method: Method;
      data?: P;
      params?: P;
      isLoading?: boolean;
    }
  ): Promise<R> {
    const { method, data, params, isLoading } = option;

    let modalReturnProps: ModalReturn | undefined;

    if (isLoading) {
      modalReturnProps = Modal.info({
        content: () => <div class="text-center">正在努力请求中...</div>,
        simple: true,
        maskClosable: false,
        footer: false,
        modalStyle: {
          width: "200px",
        },
      });
    }

    try {
      const response = await this.instance.request<ServiceResponse<R>>({
        url,
        method,
        params,
        data,
        headers: this.getDefaultHeaders(),
      });

      if (response.status !== HttpStatusCode.Ok) {
        throw new Error("网络请求异常");
      }

      switch (response.data.status) {
        case 100:
          throw new Error("您的账号已在其他地方登陆，您已被迫下线");
        case 200:
          return response.data.data!;
        case -1:
          throw new Error(response.data.msg || "请登录");
        default:
          throw new Error(response.data.msg || "网络请求失败");
      }
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      Notification.error({ content: errorMessage });
      throw new Error(errorMessage);
    } finally {
      modalReturnProps?.close();
    }
  }

  private getDefaultHeaders() {
    return {
      "Content-Type": "application/x-www-form-urlencoded",
      get Authorization() {
        return `Bearer ${state.token}`; // 动态获取最新的 token;
      },
    };
  }

  private getErrorMessage(error: any): string {
    if (error.message) {
      return error.message;
    }
    return "网络请求失败";
  }

  async post<R, P = any>(url: string, option?: ServicePostOptions<P>): Promise<R> {
    return this.send(url, {
      method: "POST",
      data: option?.data,
      isLoading: option?.isLoading,
    });
  }
}

export default new AxiosRequest({
  baseURL: import.meta.env.DEV ? "/api" : import.meta.env.VITE_BASE_DIR,
  headers: {
    brand: "jbcgk",
    // get Authorization() {
    //   console.log(state.token,'tokendenglu',Cookies.get("token"))
    //   return `Bearer ${state.token}`; // 动态获取最新的 token;
    // }
  },
});
