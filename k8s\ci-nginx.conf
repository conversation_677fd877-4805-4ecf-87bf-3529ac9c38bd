server {
  listen        80 default_server;
  server_name   _;
  root          /app;
  index         index.php index.html;
  access_log    off;
  location ~ [^/]\.php(/|$) {
     try_files $uri =404;
     fastcgi_pass  localhost:9000;
     fastcgi_index index.php;
     set $real_script_name $fastcgi_script_name;
     if ($fastcgi_script_name ~ "^(.+?\.php)(/.+)$") {
         set $real_script_name $1;
         set $path_info $2;
     }
     include fastcgi.conf;
     fastcgi_param SCRIPT_FILENAME $document_root$real_script_name;
     fastcgi_param SCRIPT_NAME $real_script_name;
     fastcgi_param PATH_INFO $path_info;
  }
  location ~ ^/(\.user.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README.md) {
     return 404;
  }
  location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
     expires      30d;
     access_log off;
  }

  location ~ .*\.(js|css)?$ {
     expires      12h;
     access_log off;
  }

  location / {
     root  /app;
     index  index.html index.htm index.php l.php;
     autoindex  off;

     if (!-e $request_filename){
        rewrite  ^/(.*)$  /index.php/$1  last;
     }
  }
}