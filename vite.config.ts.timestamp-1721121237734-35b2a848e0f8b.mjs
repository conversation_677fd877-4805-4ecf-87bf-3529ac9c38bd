// vite.config.ts
import vueJsx from "file:///Users/<USER>/Documents/%E5%85%AC%E5%8F%B8%E9%A1%B9%E7%9B%AE/%E8%80%83%E8%AF%95/my-vue-app/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { defineConfig, loadEnv } from "file:///Users/<USER>/Documents/%E5%85%AC%E5%8F%B8%E9%A1%B9%E7%9B%AE/%E8%80%83%E8%AF%95/my-vue-app/node_modules/vite/dist/node/index.js";
import { resolve } from "path";
var __vite_injected_original_dirname = "/Users/<USER>/Documents/\u516C\u53F8\u9879\u76EE/\u8003\u8BD5/my-vue-app";
var _resolve = (src) => resolve(__vite_injected_original_dirname, src);
var vite_config_default = defineConfig((config) => {
  const env = loadEnv(config.mode, process.cwd());
  console.log(env["VITE_SERVICE_URL"]);
  return {
    plugins: [vueJsx()],
    base: "/exam",
    resolve: {
      alias: {
        "@": _resolve("src"),
        "@assets": _resolve("src/assets"),
        "@common": _resolve("src/common"),
        "@apis": _resolve("src/common/apis"),
        "@router": _resolve("src/common/router"),
        "@components": _resolve("src/common/components"),
        "@store": _resolve("src/common/store"),
        "@pages": _resolve("src/pages")
      }
    },
    css: {
      preprocessorOptions: {
        less: {
          // 覆盖组件库的颜色
          modifyVars: {
            "arcoblue-6": "#447dff",
            "border-radius-small": "5px"
          }
        }
      }
    }
    // 网络代理
    // server: {
    //   proxy: {
    //     "/api": {
    //       target: env["VITE_SERVICE_URL"],
    //       changeOrigin: true,
    //       secure: false,
    //       rewrite: (path) => {
    //         console.log("请求", path);
    //         return path.replace(/^\/api/, "");
    //       },
    //     },
    //   },
    // },
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
