import { useDataStore } from "@/pages/exam/store/dataStore";
import { Checkbox } from "@arco-design/web-vue";
import { IconCheck } from "@arco-design/web-vue/es/icon";
import { computed, defineComponent } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    const currentQuestion = computed(() => dataStore.currentQuestion);

    return () => (
      <Checkbox.Group
        onChange={(e) => {
          console.log(e);
          currentQuestion.value?.answer(e as string[]);
        }}
        model-value={currentQuestion.value?.respondList}
        direction="vertical"
        class=" space-y-4 flex flex-col"
      >
        {currentQuestion.value?.options.map((e, i) => (
          <Checkbox key={e.key} value={e.key}>
            {{
              checkbox: (checkBoxE: { checked: boolean }) => {
                return (
                  <div class="flex items-center space-x-5">
                    <div
                      class={`w-5 h-5 rounded-md border-gray-400 border flex items-center justify-center text-xl ${
                        checkBoxE.checked && " border-primary text-primary"
                      }`}
                    >
                      {checkBoxE.checked && <IconCheck />}
                    </div>
                    <div
                      class="user-select-none"
                      style={{
                        fontSize: dataStore.fontSize + "px",
                      }}
                    >
                      <span class=" font-bold mr-2">{String.fromCharCode(i + 65)}.</span>
                      {e.content}
                    </div>
                  </div>
                );
              },
            }}
          </Checkbox>
        ))}
      </Checkbox.Group>
    );
  },
});
