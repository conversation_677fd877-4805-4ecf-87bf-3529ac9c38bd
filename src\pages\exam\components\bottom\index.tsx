import { defineComponent } from "vue";
import Mark from "./components/mark";
import Submit from "./components/submit";
import Switch_question from "./components/switch_question";

export default defineComponent({
  setup() {
    return () => (
      <div class="bg-white border-t border-gray-100 p-3 flex justify-between items-center">
        <div class="flex items-center space-x-10">
          <Submit />
          <Mark />
        </div>
        <Switch_question />
      </div>
    );
  },
});
