import { Login_sms_api } from "@/common/apis/apis";
import { Input, Modal } from "@arco-design/web-vue";
import image4 from "@assets/images/image4.png";
import { computed, defineComponent, reactive, ref } from "vue";
import { getTitle } from "@common/cookies/user.ts";

type InputFormData = {
  phone: string;
  code: string;
};

export default defineComponent({
  emits: {
    onNextEvent: (params: InputFormData) => params,
  },
  setup(_, ctx) {
    const isOpenTips = ref(true);

    const inputForm = reactive<InputFormData>({ phone: "", code: "" });

    const codeTime = ref(60);
    const codeTimer = ref<number>(-1);

    const codeButtonTextComputed = computed(() => {
      return codeTimer.value >= 0 ? `（${codeTime.value}s）重新获取` : "获取验证码";
    });
    const provinceTitle = getTitle();

    const handleInputChange = (value: string) => {
      console.log(value);

      inputForm.phone = value.replace(/\D/g, "");
    };

    const codeButton = () => {
      if (inputForm.phone.length < 11) {
        alert("请输入正确手机号");
        return;
      }
      if (codeTimer.value >= 0) {
        return;
      }
      const { TencentCaptcha } = window as any;
      var captcha = new TencentCaptcha("2064328247", (res: any) => {
        if (res.ret === 0) {
          // 页面上滑动正确，请求自己的业务接口
          // 记得把验证成功的票据和随机字符带到自己接口中去腾讯验证票据的真实性
          checkTencentCode(res.ticket, res.randstr, inputForm.phone);
        }
      });
      captcha.langFun();
      // 滑块显示
      captcha.show();
    };

    // 验图形验证码且发送短信

    const checkTencentCode = async (ticket: String, randstr: String, phone: String) => {
      const params = {
        ticket: ticket,
        randstr: randstr,
        mobile: phone,
      };

      await Login_sms_api.api({ data: params });

      // if (respose?.status !== 200) {
      //   console.log("图形验证码异常");
      //   return;
      // }
      startCodeTimer();
    };

    // 开启验证码计算器
    const startCodeTimer = () => {
      codeTime.value = codeTime.value - 1;
      if (codeTimer.value >= 0) return;
      codeTimer.value = setInterval(() => {
        codeTime.value = codeTime.value - 1;
        if (codeTime.value <= 0) {
          clearInterval(codeTimer.value as number);
          codeTime.value = 60;
          codeTimer.value = -1;
          return -1;
        }
      }, 1000) as unknown as number;
    };
    return () => (
      <div class=" w-full h-full flex flex-col items-center justify-center space-y-8 -mt-28">
        <Modal
          v-model:visible={isOpenTips.value}
          hideTitle
          simple
          title={undefined}
          escToClose={false}
          maskClosable={false}
          hide-cancel
          width="800px"
          okButtonProps={{
            size: "large",
          }}
        >
          <div class=" space-y-4  text-sm">
            <div>注意事项：</div>
            {/* <div>请使用以下身份证号登录本机考练习系统，进行机考模拟练习：</div>
            <div class="pl-4">
              身份证号：<span class="">1234</span>
            </div> */}
            <div>
              浏览器要求为Edge 87及以上，Chrome 87及以上，Firefox 80及以上，Mac Safari
              12及以上或其他对应内核的浏览器。
            </div>
            <div>
              打开本模拟考试系统后，请按F11键
              开启全屏模式（测试版可以通过最小化等操作切换到计算机桌面，而正式版考试时，考生无法切换屏幕，屏幕会被锁定，这里的F11开启全屏为模拟正式考试场景，优化测试体验）。
            </div>
            <div>阅读完注意事项后，请点击下方“确定”按钮。</div>
          </div>
        </Modal>
        <div
          class="font-bold text-5xl text-gray-800 cor-f"
          style="margin-bottom: 0;color:#0261BE!important;text-shadow:-1px -1px 0 #fff,1px -1px 0 #fff,-1px  1px 0 #fff,1px  1px 0 #fff;"
        >
          {provinceTitle}
        </div>
        <div class=" text-center text-lg text-red-500" style="margin-top: 34px">
          <div
            style="
            box-sizing:border-box;
            padding: 6px 16px;
            background: #FFFFFF;
            border-radius: 19px;
            font-size: 16px;
            color: #FF2A00;
            font-weight: 400;
            display:flex;
            align-items: center;
            justify-content: center;
            opacity: 0.5;"
          >
            说明：非官方系统，仅用于模拟
          </div>
          {/*<div>*/}
          {/*  系统中所展示的试卷结构、题目数量、试题长度及难度等均不具实际参考意义。*/}
          {/*</div>*/}
          {/*<div>界面样式可能与正式考试有所不同。</div>*/}
        </div>
        {/* <div class=" space-y-3">
          <div
            class="text-2xl text-center text-gray-800"
            style="letter-spacing:5px;"
          >
            身份证号
          </div>
          <Input
            size="large"
            placeholder="请输入身份证号"
            defaultValue="1234"
            disabled
            class=" border-gray-200 border w-72 rounded-none text-xl"
          />
        </div> */}

        <div class="text-2xl text-center text-gray-800" style="letter-spacing:5px;margin-top:60px">
          手机号
        </div>
        <div style="margin-top:20px">
          <Input
            size="large"
            placeholder="请输入手机号"
            max-length={11}
            v-model={inputForm.phone}
            onInput={handleInputChange}
            // defaultValue="1234"
            // inputForm
            class=" border-gray-200 border w-72 rounded-none text-xl"
          />
        </div>
        <div style="margin-top:20px">
          <Input
            size="large"
            placeholder="请输入验证码"
            v-model={inputForm.code}
            max-length={4}
            // defaultValue="1234"
            // inputForm
            class=" border-gray-200 border w-72 rounded-none text-xl"
            v-slots={{
              suffix: () => (
                <div
                  style="font-size: 16px;cursor:pointer"
                  onClick={() => {
                    codeButton();
                  }}
                >
                  {codeButtonTextComputed.value}
                </div>
              ),
            }}
          ></Input>
        </div>
        <div>
          <div
            onClick={() => {
              ctx.emit("onNextEvent", inputForm);
            }}
            class="text-sm text-white mt-6 letterspacing3 w-24 text-center flex items-center justify-center h-8 cursor-pointer"
            style="background-color:#01499d;"
          >
            登录
          </div>
          {/* <Button
            onClick={() => {
              ctx.emit("onNextEvent");
            }}
            size="large"
            class="px-10 mt-6"
            type="primary"
          >
            登录
          </Button> */}
        </div>
      </div>
    );
  },
});
