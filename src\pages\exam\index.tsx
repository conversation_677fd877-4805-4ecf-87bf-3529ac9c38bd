import { defineComponent, onMounted, onUnmounted } from "vue";
import Bottom from "./components/bottom";
import Center from "./components/center";
import Top from "./components/top";
import "./index.scss";
import { useDataStore } from "./store/dataStore";
export default defineComponent({
  setup() {
    // 初始化获取数据
    const dataStore = useDataStore();

    dataStore.init();
    setTimeout(() => {
      dataStore.setTimeCount();
      dataStore.changeCurrentToIndex(0);
    }, 500);
    //监听 浏览器窗口关闭事件，触发日志上传
    onMounted(() => {
      window.addEventListener("beforeunload", (e) => beforeunloadHandler(e));
      window.addEventListener("unload", (e) => unloadHandler(e));
    });
    onUnmounted(() => {
      window.removeEventListener("beforeunload", (e) => beforeunloadHandler(e));
      window.addEventListener("unload", (e) => unloadHandler(e));
    });

    const beforeunloadHandler = async (event: any) => {
      if (dataStore.isSubmit) {
        return;
      }
      //网页上 显示确认对话框
      event.preventDefault(); // 取消默认的关闭操作
      event.returnValue = "确认退出模考吗"; // Chrome要求设置返回值才能触发提示框
      return;
    };
    const unloadHandler = async (event: any) => {
      if (dataStore.isSubmit) {
        return;
      }
      event.preventDefault(); // 取消默认的关闭操作
      event.returnValue = "确认退出模考吗"; // Chrome要求设置返回值才能触发提示框
      return;
    };

    return () => (
      <div class="w-screen h-screen flex flex-col">
        <Top />
        <Center />
        <Bottom />
      </div>
    );
  },
});
