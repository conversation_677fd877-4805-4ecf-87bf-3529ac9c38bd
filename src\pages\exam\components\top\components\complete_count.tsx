import { useDataStore } from "@/pages/exam/store/dataStore";
import { computed, defineComponent } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    // 已完成数量
    const completeCount = computed(
      () => dataStore.questionList.filter((e) => e.isAnswerComplete).length
    );

    return () => (
      <div class="text-white font-bold">
        已完成{completeCount.value}/{dataStore.questionList.length}题
      </div>
    );
  },
});
