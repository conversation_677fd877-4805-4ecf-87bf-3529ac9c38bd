import { useExamInfoStore } from "@/common/store/examInfoStore";
import { <PERSON><PERSON>, Modal, Notification } from "@arco-design/web-vue";
import { ElSelect, ElOption } from "element-plus";
import "element-plus/dist/index.css";
import image32 from "@assets/images/image32.png";
import { Job_api, Job_list_api, paper_info_api } from "@/common/apis/apis";
import { setEndTime, setType, setUserPositionCookie, getTitle } from "@/common/cookies/user";
import { defineComponent, onUnmounted, reactive, ref, onMounted, watch, computed } from "vue";
import { useRoute } from "vue-router";

interface JobCategory {
  key: string | undefined;
  name: string; // 假设 select_option 中有 name 属性
}

interface SelectOption {
  id: number;
  name: string;
}

export default defineComponent({
  emits: {
    backEvent: () => null,
    nextEvent: () => null,
  },
  props: {
    current: Number,
  },
  setup(props, context) {
    const time = ref(import.meta.env.DEV ? 1 : 10);
    const timer = setInterval(() => {
      if (time.value <= 0) {
        clearInterval(timer);
      }
      time.value -= 1;
    }, 1000);

    const examInfoStore = useExamInfoStore();
    const examInfo = reactive<any>(examInfoStore.info);
    const route = useRoute();
    const jobCategories = ref<JobCategory[]>([]);
    const jobOptionsList = ref<SelectOption[][]>([]); // 初始化为二维数组
    const selectedJobIds = ref<any>([]);
    const showJobSelection = ref(false); //判断是否选岗
    const paperInfo = ref<any>({});
    const isRequest = ref(false);
    const isRequestALL = ref(false);
    const selectListStyle = ref<any>({});
    const selectListRef = ref<HTMLElement | null>(null);
    const provinceTitle = getTitle();

    const currentValue = ref(props.current);

    const resetAfterSelection = (categoryIndex: number) => {
      console.log("重置");

      console.log(selectedJobIds.value, jobCategories.value);

      for (let index = categoryIndex + 1; index < jobCategories.value.length; index++) {
        selectedJobIds.value[index] = undefined;
        jobOptionsList.value[index] = [];
      }
    };

    const handleChange = (value: number, index: number) => {
      selectedJobIds.value[index] = value;

      if (jobCategories.value[index]?.key) {
        resetAfterSelection(index);
      }
      setTimeout(() => {
        getJobList(index + 1);
      });
    };

    // 选岗列表
    const getJob = async () => {
      if (paperInfo.value.has_job === 1 && paperInfo.value.type === 1) {
        try {
          await Job_api.api({
            data: {
              examkey: route.params.examKey as string,
            },
          }).then((res: any) => {
            console.log(res);
            jobCategories.value = res.select_option;
            // 初始化 jobOptionsList 为与 jobCategories 相同长度的空数组
          });
        } catch (error) {
          console.log("请求失败");
        }
      }
      isRequestALL.value = true;
    };
    // 选岗选项列表
    const getJobList = async (categoryIndex: any) => {
      if (!jobCategories.value[categoryIndex]?.key) return;
      const params = {
        examkey: route.params.examKey as string,
        field: jobCategories.value[categoryIndex].key,
      };
      // 构建参数对象，包含之前所选岗位的ID
      for (let index = 0; index < categoryIndex; index++) {
        const job = jobCategories.value[index];
        if (job.key && selectedJobIds.value[index]) {
          params[job.key] = selectedJobIds.value[index];
        }
      }

      try {
        await Job_list_api.api({
          data: params,
        }).then((res: any) => {
          jobOptionsList.value[categoryIndex] = res || [];
          console.log(jobOptionsList.value, "xunxiang");
        });
      } catch (error) {
        console.log("请求失败");
      }
    };

    // 获取试卷信息
    const getPaperInfo = async () => {
      try {
        await paper_info_api
          .api({
            data: {
              examkey: route.params.examKey as string,
            },
          })
          .then((res: any) => {
            paperInfo.value = res;
            if (res.has_job === 1 && res.type === 1) {
              showJobSelection.value = true;
            }
            setEndTime(res.end_time);
            setType(res.type);
            console.log(res, "试卷信息", res.has_job, showJobSelection.value, paperInfo.value);
            isRequest.value = true;
          });
      } catch (error) {
        isRequest.value = true;
        console.log("请求失败");
      }
    };

    // const selectListStyle = computed(() => {
    //   if (selectListHeight.value !== null) {
    //     return {
    //       height: `${selectListHeight.value}px`,
    //     };
    //   }
    //   return {};
    // });

    // 有选岗时判断按钮
    const isDisabledButton = computed(() => {
      let result = false;
      for (const index in jobCategories.value) {
        if (!selectedJobIds.value[index]) {
          result = true;
        }
      }
      return result;
    });

    onMounted(async () => {});

    onUnmounted(() => {
      clearInterval(timer);
    });

    watch(
      () => props.current,
      async (newVal) => {
        currentValue.value = newVal;
        await getPaperInfo();
        await getJob();
        getJobList(0);
      },
      { immediate: true }
    );

    const isTimestampInFuture = (timestamp: number): boolean => {
      // 将10位数的时间戳转换为毫秒
      const timestampInMilliseconds = timestamp * 1000;

      // 获取当前时间的毫秒数
      const currentTimestamp = Date.now();
      console.log(timestampInMilliseconds, currentTimestamp, "时间比较");
      // 比较时间戳
      return timestampInMilliseconds > currentTimestamp;
    };

    const handleSelectClick = (index: any) => {
      console.log(index, "索引");
      if (index >= 1 && !selectedJobIds.value[index - 1]) {
        const tips = jobCategories.value[index - 1].name;
        // Message.normal({
        //   content: `请先选择21121${tips}`,
        //   showIcon: false,
        // });
        Notification.error({ content: `请先选择${tips}` });
      }
    };

    return () => (
      <div class="w-full h-full flex flex-col items-center justify-center space-y-8">
        {isRequestALL.value && (
          <div class="flex flex-col items-center">
            <div class="font-bold text-5xl text-gray-800 cor-f" style="color:#2588e8 !important">
              {provinceTitle || ""}
            </div>
            <div
              class={`bg-white w-[860px] rounded-xl flex flex-col  space-y-14 content-box ${
                showJobSelection.value ? "w-1000" : ""
              }`}
            >
              <div class="flex main-content items-center">
                <div class={`flex1 flex flex-col ${showJobSelection.value ? "" : "no-job"}`}>
                  <div class="title-t">考生信息</div>
                  <div class="flex items-center space-x-10 flex1 mt32">
                    <img src={image32} class="w-40 w140" alt="" />
                    <div class="space-y-4 text-gray-800 text-sm fonts">
                      <div class="flex ">
                        <p
                          class="text-justify pr-1 f16 w-100"
                          style="text-align-last: justify;display: inline;;color:#949699"
                        >
                          姓　　名:
                        </p>
                        <p class="pl-4 flex1" style="color:#393F47;display: inline">
                          {examInfo.name}
                        </p>
                      </div>
                      <div class="f16 flex">
                        <p
                          class="text-justify pr-1 f16 w-100"
                          style="text-align-last: justify;color:#949699"
                        >
                          考试名称:
                        </p>
                        <p class="pl-4 flex1" style="color:#393F47;display: inline">
                          {examInfo.exam_name}
                        </p>
                      </div>
                      <div class="flex">
                        <p
                          class="text-justify pr-1 f16 w-100"
                          style="text-align-last: justify;display: inline;color:#949699"
                        >
                          考试时间:
                        </p>
                        <p class="pl-4 flex1" style="color:#393F47;display: inline">
                          {examInfo.start_time}-{examInfo.end_time}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                {showJobSelection.value && (
                  <div
                    class="select-list  flex flex-col"
                    style={selectListStyle.value}
                    ref={selectListRef}
                  >
                    <div class="title-t">选择岗位</div>
                    <div class="mt32">
                      {jobCategories.value.map((jobCategory, index) => (
                        <div
                          class="select-wrapper"
                          onClick={() => handleSelectClick(index)} // 在外部 div 上绑定点击事件
                        >
                          {/* <Select
                            model-value={selectedJobIds.value[index]}
                            style={{
                              width: "360px",
                              pointerEvents: !jobOptionsList.value[index]
                                ?.length
                                ? "none"
                                : "auto",
                            }}
                            class="select-list-item"
                            size={"large"}
                            placeholder={`请选择${jobCategory.name}`}
                            disabled={!jobOptionsList.value[index]?.length}
                            onChange={(val: any) => handleChange(val, index)}
                          >
                            {jobOptionsList.value[index]?.map(
                              (jobOption, sencondIndex) => (
                                <Option key={sencondIndex} value={jobOption.id}>
                                  {jobOption.name}
                                </Option>
                              )
                            )}
                          </Select> */}
                          <ElSelect
                            modelValue={selectedJobIds.value[index]}
                            style={{ width: "360px" }}
                            size="large"
                            placeholder={`请选择${jobCategory.name}`}
                            disabled={!jobOptionsList.value[index]?.length}
                            onChange={(val: any) => handleChange(val, index)}
                          >
                            {jobOptionsList.value[index]?.map((jobOption, sencondIndex) => (
                              <ElOption
                                key={sencondIndex}
                                value={jobOption.id}
                                label={jobOption.name}
                              />
                            ))}
                          </ElSelect>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div class="bottom-box flex items-center justify-center">
                {isRequest.value && !isTimestampInFuture(paperInfo.value.start_time) ? (
                  <>
                    <Button
                      disabled={
                        // 如果 has_job 等于 0，只根据 time.value 来判断
                        paperInfo.value?.has_job === 0
                          ? time.value > 0
                          : // 如果 has_job 等于 1 并且 type 等于 1，根据 time.value 和 isDisabledButton.value 来判断
                          paperInfo.value?.has_job === 1 && paperInfo.value.type === 1
                          ? time.value > 0 || isDisabledButton.value
                          : // 在其他情况下，也只根据 time.value 来判断
                            time.value > 0
                      }
                      onClick={() => {
                        if (paperInfo.value.has_job === 1 && paperInfo.value.type === 1) {
                          const position = JSON.stringify({
                            [route.params.examKey as string]:
                              selectedJobIds.value[selectedJobIds.value.length - 1],
                          });
                          setUserPositionCookie(position);
                          console.log(position, "选择后");
                        }
                        context.emit("nextEvent");
                      }}
                      size="large"
                      type="primary"
                      class="btn-s"
                    >
                      确认{time.value > 0 ? time.value : ""}
                    </Button>
                    <Button
                      onClick={() => {
                        Modal.confirm({
                          alignCenter: true,
                          simple: true,
                          content: () => <div class="text-center">确认并返回登录页？</div>,
                          onOk() {
                            context.emit("backEvent");
                          },
                        });
                      }}
                      size="large"
                      class="btn-s"
                    >
                      取消
                    </Button>
                  </>
                ) : (
                  <Button disabled size="large" class="btn-s btn-240" type="primary">
                    模考未开始
                  </Button>
                )}
              </div>
            </div>
            <div class="bottom-b px-4 text-gray-700 py-0.5 rounded-full">
              若您的个人信息存在错误，请联系现场监考人员进行处理
            </div>
          </div>
        )}
      </div>
    );
  },
});
