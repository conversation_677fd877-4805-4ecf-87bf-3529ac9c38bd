import { defineComponent, ref, watch, onBeforeUnmount } from "vue";
import Editor from "@tinymce/tinymce-vue";

export default defineComponent({
  props: {
    modelValue: String,
  },
  emits: ["update:modelValue"],
  setup(props, ctx) {
    const html = ref("");
    let editorInstance: any = null; // 用来存储编辑器实例

    // 监听props变化同步到html
    watch(
      () => props.modelValue,
      (value) => {
        if (value !== html.value) {
          html.value = value ?? "";
        }
      },
      { immediate: true }
    );

    // 监听html变化并更新modelValue
    watch(
      () => html.value,
      (value) => {
        ctx.emit("update:modelValue", value);
      }
    );

    function onTinymceInput(instance: any) {
      const containerEl = instance.getContainer();

      containerEl
        .querySelector(".tox-statusbar__path")
        .insertAdjacentHTML(
          "afterend",
          `<button type="button" class="tox-statusbar__wordcount"></button>`
        );

      instance.on(
        "input",
        function (e: any) {
          const contentEl = instance.getContent({ format: "text" });
          console.log(contentEl);

          const countEl = containerEl.querySelector(".tox-statusbar__wordcount");
          countEl.innerHTML = "字符数: " + stripHtmlTags(contentEl).length;
        },
        1000
      );
    }

    // 移除字符串中的HTML标签;
    function stripHtmlTags(str: string) {
      // 使用正则表达表匹配所有HTML标签并替换为空字符串
      let result = str.replace(/<[^>]+>/g, "");
      // result = result.replace(/&nbsp;/g, " ");
      // result = result.replace(/&ldquo;/g, "“");
      // result = result.replace(/&lsquo;/g, "‘");
      // result = result.replace(/&rsquo;/g, "’");
      // result = result.replace(/&hellip;/g, ".");
      // result = result.replace(/&hellip;/g, ".");
      // result = result.replace(/&amp;/g, "&");
      // result = result.replace(/&lt;/g, "<");
      // result = result.replace(/&gt;/g, ">");
      // result = result.replace(/&mdash;/g, "—");

      result = result.replace(/\s+/g, "");

      return result;
    }

    return () => (
      <Editor
        tinymceScriptSrc={`${import.meta.env.VITE_BASE_URL}/tinymce/tinymce.js`}
        api-key="rsriu19x1reflxnibcxnskr4ghyqr7tagcf6mi2x0i6p06ji"
        init={{
          base_url: `${import.meta.env.VITE_BASE_URL}/tinymce/`,
          menubar: false,
          height: 500,
          plugins: "kityformula-editor charmap preview fullscreen ",
          language_url: `${import.meta.env.VITE_BASE_URL}/tinymce/zh-Hans.js`,
          language: "zh-Hans",
          toolbar: "copy | cut | paste | alignCenter | alignLeft | alignRight",
          license_key: "gpl",
          setup: (editor: any) => {
            // 存储编辑器实例
            editorInstance = editor;

            setTimeout(() => {
              onTinymceInput(editor);
            }, 1000);
          },
        }}
        v-model={html.value}
      />
    );
  },
});
