import { useDataStore } from "@/pages/exam/store/dataStore";
import { Button, Countdown, Modal } from "@arco-design/web-vue";
import { IconPause } from "@arco-design/web-vue/es/icon";
// import { isEmpty } from "lodash";
import { defineComponent, ref } from "vue";
import "./index.scss";
import { getExam, getProvince } from "@common/cookies/user.ts";
export default defineComponent({
  setup() {
    function onLock() {
      const { close } = Modal.open({
        fullscreen: true,
        title: () => <div></div>,
        footer: () => <div></div>,
        simple: true,
        escToClose: false,
        content: () => <BuildLockPopup onClose={() => close()} />,
        modalClass: "bg-transparent lock_modal",
      });
      const dataStore = useDataStore();
      dataStore.isLock = true;
    }
    return () => (
      <div
        onClick={onLock}
        class="flex hover items-center leading-none text-white border rounded-full px-2 py-1"
      >
        <IconPause class=" text-lg" />
        <span>暂时锁屏</span>
      </div>
    );
  },
});

// 锁屏弹窗
const BuildLockPopup = defineComponent({
  emits: {
    close: () => true,
  },
  setup(_, ctx) {
    const dataStore = useDataStore();
    const title = dataStore.provinceTitle;
    // 倒计时函数
    const countdown = ref(60);
    let intervalId: any = null;
    function countdownText() {
      if (countdown.value > 0) {
        return `考试时间已结束，将在 ${countdown.value}s 后自动交卷`;
      } else {
        return "考试时间已结束，将在 0s 后自动交卷";
      }
    }
    const startCountdown = () => {
      intervalId = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value -= 1;
        }
        if (countdown.value == 0) {
          countdown.value = -1;
          dataStore.submitInfo();
          clearInterval(intervalId);
        }
      }, 1000);
    };

    // const code = ref("");

    return () => (
      <div class=" relative flex flex-col  pt-52 items-center space-y-5">
        <div class={`text-[30px] text-black font-bold`}>{title}</div>
        <div class=" flex flex-col items-center px-20 pt-14 pb-20 bg-white rounded-xl space-y-5 shadow-xl shadow-primary/10">
          <div class="flex items-center space-x-3">
            <div class=" text-xl text-gray-400 space-x-2 font-bold">
              <span class="iconfont icon-suo"></span>
              <span>考生暂离锁屏</span>
            </div>
            <div class=" text-white bg-gray-400 rounded-md px-2 py-1 ">
              <Countdown
                key={dataStore.testPaperSource?.test_time}
                value={
                  Date.now() +
                  (Number(dataStore.testPaperSource?.test_time ?? 0) - dataStore.used_time) * 1000
                }
                format="剩余：H 时 m 分 s 秒"
                onFinish={() => {
                  // 定义倒计时的计算属性
                  startCountdown();
                  const { close } = Modal.open({
                    title: "提示",
                    content: countdownText,
                    simple: true,
                    okText: "立即交卷",
                    cancelText: "放弃交卷",
                    escToClose: false,
                    maskClosable: false,
                    onBeforeCancel(): any {
                      const { close: closeTwo } = Modal.open({
                        title: "提示",
                        content: "确认放弃交卷吗，本次作答记录将不会保存",
                        simple: true,
                        okText: "确定",
                        cancelText: "取消",
                        escToClose: false,
                        maskClosable: false,
                        onCancel() {
                          closeTwo();
                        },
                        onOk() {
                          close();
                          closeTwo();
                          dataStore.isSubmit = true;
                          let exam = getExam();
                          let province = getProvince();
                          const domain = window.location.hostname;
                          const brand = document.cookie.replace(
                            /(?:(?:^|.*;\s*)brand\s*\=\s*([^;]*).*$)|^.*$/,
                            "$1"
                          );
                          const path = `/wp/practice/exam_detail?brand=${brand ? brand : "jbcgk"}&province=${province}&exam=${exam}&paper_id=${
                            dataStore.route.params.examKey as string
                          }`;
                          window.location.href = "https://" + domain + path;
                        },
                      });
                    },
                    onOk() {
                      close();
                      dataStore.submitInfo();
                    },
                  });
                }}
                valueStyle={{
                  fontSize: "14px",
                  color: "#ffffff",
                }}
              />
            </div>
          </div>

          <div class="space-x-3 flex items-center w-[600px] " style="justify-content: center">
            {/* <Input
              class=" w-[600px]"
              v-model={code.value}
              style={{}}
              size="large"
              placeholder="输入身份证号，继续考试"
            /> */}
            <Button
              onClick={() => {
                // if (dataStore.questionSource?.prove != code.value) {
                //   return Message.error("考号不匹配");
                // }
                ctx.emit("close");
                dataStore.isLock = false;
              }}
              // disabled={isEmpty(code.value)}
              size="large"
              type="primary"
            >
              继续考试
            </Button>
          </div>
        </div>
      </div>
    );
  },
});
