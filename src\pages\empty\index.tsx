// import image3 from "@assets/images/image3.jpg";
import image404 from "@assets/images/404.png";

import { defineComponent } from "vue";

export default defineComponent(() => {
  return () => (
    <div
      class="w-screen h-screen bg-cover relative flex flex-col items-center justify-center"
      style="background:#F2F3F5"
      // style={{
      //   backgroundImage: `url(${image3})`,
      // }}
    >
      {/* <div class=" absolute right-5 top-5 text-8xl text-red-400">01</div> */}
      <img src={image404} alt="" srcset="" style="margin-top:-200px" />
      {/* <div class=" text-6xl font-bold">考试已结束</div> */}
      <div style="font-size: 14px;color: #434343;">您所访问的页面不存在</div>
    </div>
  );
});
