import { useDataStore } from "@/pages/exam/store/dataStore";
import { Question } from "@/pages/exam/store/model/question_model";
import { computed, defineComponent } from "vue";
import Multiple_choice_answer from "./components/multiple_choice_answer";
import Single_choice_answer from "./components/single_choice_answer";
import Writing_question_answer from "./components/writing_question_answer";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    const currentQuestion = computed(() => {
      return dataStore.currentQuestion;
    });

    return () => {
      // 单选
      if (currentQuestion.value instanceof Question.SingleChoiceQuestionModel) {
        return <Single_choice_answer />;
      }
      // 判断题
      if (currentQuestion.value instanceof Question.CheckQuestionModel) {
        return <Single_choice_answer />;
      }
      // 多选
      if (currentQuestion.value instanceof Question.MultipleChoiceQuestionModel) {
        return <Multiple_choice_answer />;
      }
      // 自己写的题
      if (currentQuestion.value instanceof Question.WritingQuestionModel) {
        return <Writing_question_answer />;
      }

      return <div>未知</div>;
    };
  },
});
