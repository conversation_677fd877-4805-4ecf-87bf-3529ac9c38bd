import { useDataStore } from "@/pages/exam/store/dataStore";
import { Radio } from "@arco-design/web-vue";
import { isEmpty } from "lodash";
import { computed, defineComponent, onMounted, onUnmounted, ref } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();
    const isKeyboardEventActive = ref(false);

    function handleKeyboardEvent(event: any) {
      if (
        event.key === "ArrowUp" ||
        event.key === "ArrowDown" ||
        event.key === "ArrowLeft" ||
        event.key === "ArrowRight"
      ) {
        isKeyboardEventActive.value = true;
        setTimeout(() => {
          isKeyboardEventActive.value = false;
        }, 500);
      }
    }

    onMounted(() => {
      window.addEventListener("keydown", handleKeyboardEvent);
    });

    onUnmounted(() => {
      window.removeEventListener("keydown", handleKeyboardEvent);
    });

    function onChange(e: any) {
      if (!isKeyboardEventActive.value) {
        currentQuestion.value?.answer([e as string]);
      }
      isKeyboardEventActive.value = false;
    }
    const currentQuestion = computed(() => dataStore.currentQuestion);
    return () => (
      <Radio.Group
        onChange={onChange}
        modelValue={
          isEmpty(currentQuestion.value?.respondList) ? "" : currentQuestion.value?.respondList[0]
        }
        direction="vertical"
        size="large"
        class="space-y-3"
      >
        {currentQuestion.value?.options.map((e, i) => (
          <Radio key={e.key} value={e.key}>
            {{
              radio: (radioE: { checked: boolean }) => {
                return (
                  <div class="flex items-center space-x-5">
                    <div
                      class={`w-5 h-5  rounded-full border-gray-400 border flex items-center justify-center text-xl ${
                        radioE.checked && " border-primary text-primary"
                      }`}
                      style={"min-width: 20px"}
                    >
                      {radioE.checked && <div class=" w-3 h-3 rounded-full bg-primary"></div>}
                    </div>
                    <div
                      class="user-select-none"
                      style={{
                        fontSize: dataStore.fontSize + "px",
                      }}
                    >
                      <span class=" font-bold mr-2">{String.fromCharCode(i + 65)}.</span>
                      {e.content}
                    </div>
                  </div>
                );
              },
            }}
          </Radio>
        ))}
      </Radio.Group>
    );
  },
});
