tinymce.Resource.add('tinymce.html-i18n.help-keynav.fr_FR',
'<h1>Débuter la navigation au clavier</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Cibler la barre du menu</dt>\n' +
  '  <dd>Windows ou Linux : Alt+F9</dd>\n' +
  '  <dd>macOS : &#x2325;F9</dd>\n' +
  "  <dt>Cibler la barre d'outils</dt>\n" +
  '  <dd>Windows ou Linux : Alt+F10</dd>\n' +
  '  <dd>macOS : &#x2325;F10</dd>\n' +
  '  <dt>Cibler le pied de page</dt>\n' +
  '  <dd>Windows ou Linux : Alt+F11</dd>\n' +
  '  <dd>macOS : &#x2325;F11</dd>\n' +
  "  <dt>Cibler une barre d'outils contextuelle</dt>\n" +
  '  <dd>Windows, Linux ou macOS : Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  "<p>La navigation débutera sur le premier élément de l'interface utilisateur, qui sera mis en surbrillance ou bien souligné dans le cas du premier élément du\n" +
  "  chemin d'éléments du pied de page.</p>\n" +
  '\n' +
  "<h1>Naviguer entre les sections de l'interface utilisateur</h1>\n" +
  '\n' +
  "<p>Pour passer d'une section de l'interface utilisateur à la suivante, appuyez sur <strong>Tabulation</strong>.</p>\n" +
  '\n' +
  "<p>Pour passer d'une section de l'interface utilisateur à la précédente, appuyez sur <strong>Maj+Tabulation</strong>.</p>\n" +
  '\n' +
  "<p>L'ordre de <strong>Tabulation</strong> de ces sections de l'interface utilisateur est le suivant :</p>\n" +
  '\n' +
  '<ol>\n' +
  '  <li>Barre du menu</li>\n' +
  "  <li>Chaque groupe de barres d'outils</li>\n" +
  '  <li>Barre latérale</li>\n' +
  "  <li>Chemin d'éléments du pied de page</li>\n" +
  "  <li>Bouton d'activation du compteur de mots dans le pied de page</li>\n" +
  '  <li>Lien de marque dans le pied de page</li>\n' +
  "  <li>Poignée de redimensionnement de l'éditeur dans le pied de page</li>\n" +
  '</ol>\n' +
  '\n' +
  "<p>Si une section de l'interface utilisateur n'est pas présente, elle sera ignorée.</p>\n" +
  '\n' +
  "<p>Si le pied de page comporte un ciblage par navigation au clavier et qu'il n'y a aucune barre latérale visible, appuyer sur <strong>Maj+Tabulation</strong>\n" +
  "  déplace le ciblage vers le premier groupe de barres d'outils et non le dernier.</p>\n" +
  '\n' +
  "<h1>Naviguer au sein des sections de l'interface utilisateur</h1>\n" +
  '\n' +
  "<p>Pour passer d'un élément de l'interface utilisateur au suivant, appuyez sur la <strong>Flèche</strong> appropriée.</p>\n" +
  '\n' +
  '<p>Les touches fléchées <strong>Gauche</strong> et <strong>Droite</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>se déplacent entre les menus de la barre des menus.</li>\n' +
  "  <li>ouvrent un sous-menu au sein d'un menu.</li>\n" +
  "  <li>se déplacent entre les boutons d'un groupe de barres d'outils.</li>\n" +
  "  <li>se déplacent entre les éléments du chemin d'éléments du pied de page.</li>\n" +
  '</ul>\n' +
  '\n' +
  '<p>Les touches fléchées <strong>Bas</strong> et <strong>Haut</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  "  <li>se déplacent entre les éléments de menu au sein d'un menu.</li>\n" +
  "  <li>se déplacent entre les éléments au sein d'un menu contextuel de barre d'outils.</li>\n" +
  '</ul>\n' +
  '\n' +
  "<p>Les <strong>Flèches</strong> parcourent la section de l'interface utilisateur ciblée.</p>\n" +
  '\n' +
  '<p>Pour fermer un menu ouvert, un sous-menu ouvert ou un menu contextuel ouvert, appuyez sur <strong>Echap</strong>.</p>\n' +
  '\n' +
  "<p>Si l'actuel ciblage se trouve en « haut » d'une section spécifique de l'interface utilisateur, appuyer sur <strong>Echap</strong> permet également de quitter\n" +
  '  entièrement la navigation au clavier.</p>\n' +
  '\n' +
  "<h1>Exécuter un élément de menu ou un bouton de barre d'outils</h1>\n" +
  '\n' +
  "<p>Lorsque l'élément de menu ou le bouton de barre d'outils désiré est mis en surbrillance, appuyez sur la touche <strong>Retour arrière</strong>, <strong>Entrée</strong>\n" +
  "  ou la <strong>Barre d'espace</strong> pour exécuter l'élément.</p>\n" +
  '\n' +
  '<h1>Naviguer au sein de dialogues sans onglets</h1>\n' +
  '\n' +
  "<p>Dans les dialogues sans onglets, le premier composant interactif est ciblé lorsque le dialogue s'ouvre.</p>\n" +
  '\n' +
  '<p>Naviguez entre les composants du dialogue interactif en appuyant sur <strong>Tabulation</strong> ou <strong>Maj+Tabulation</strong>.</p>\n' +
  '\n' +
  '<h1>Naviguer au sein de dialogues avec onglets</h1>\n' +
  '\n' +
  "<p>Dans les dialogues avec onglets, le premier bouton du menu de l'onglet est ciblé lorsque le dialogue s'ouvre.</p>\n" +
  '\n' +
  '<p>Naviguez entre les composants interactifs de cet onglet de dialogue en appuyant sur <strong>Tabulation</strong> ou\n' +
  '  <strong>Maj+Tabulation</strong>.</p>\n' +
  '\n' +
  "<p>Passez à un autre onglet de dialogue en ciblant le menu de l'onglet et en appuyant sur la <strong>Flèche</strong>\n" +
  '  appropriée pour parcourir les onglets disponibles.</p>\n');