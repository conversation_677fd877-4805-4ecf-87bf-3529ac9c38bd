import { useDataStore } from "@/pages/exam/store/dataStore";
import { Button } from "@arco-design/web-vue";
import { defineComponent } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();
    return () => (
      <Button
        onClick={dataStore.submit}
        type="primary"
        size="large"
        style={{
          padding: "0 5.5em",
        }}
        class=" text-[16px]"
      >
        交卷
      </Button>
    );
  },
});
