// import { toChinese } from "@/common/utils/stringUtils";
import { useDataStore } from "@/pages/exam/store/dataStore";
import { Question } from "@/pages/exam/store/model/question_model";
import { Divider, Empty } from "@arco-design/web-vue";
// import { useEventListener } from "@vueuse/core";
import { defineComponent } from "vue";

export default defineComponent({
  setup() {
    // const dataStore = useDataStore();

    // useEventListener(document, "keydown", (e) => {
    //   switch (e.key) {
    //     case "ArrowLeft":
    //       dataStore.changeCurrentToIndex(dataStore.currentIndex - 1);
    //       break;
    //     case "ArrowRight":
    //       dataStore.changeCurrentToIndex(dataStore.currentIndex + 1);
    //       break;
    //   }
    // });
    return () => (
      <div class="flex-1  space-y-3 flex flex-col">
        <BuildSubjectName />
        <Divider />
        <BuildQuestionGroupList />
        <Divider />
        <BuildTips />
      </div>
    );
  },
});

// 科目名
const BuildSubjectName = defineComponent({
  setup() {
    const dataStore = useDataStore();

    // return () => (
    //   <div class="space-x-1 font-bold text-base flex items-center">
    //     <svg
    //       // @ts-ignore
    //       t="1721703108639"
    //       class=" w-5 h-5"
    //       viewBox="0 0 1024 1024"
    //       version="1.1"
    //       xmlns="http://www.w3.org/2000/svg"
    //       p-id="7811"
    //       width="64"
    //       height="64"
    //     >
    //       <path
    //         d="M512 931.84c-20.48 0-40.96-15.36-40.96-40.96s-10.24-46.08-25.6-61.44-35.84-20.48-61.44-20.48H87.04c-20.48 0-40.96-15.36-40.96-40.96V128c0-20.48 15.36-40.96 40.96-40.96h256c56.32 0 107.52 20.48 148.48 61.44 40.96 40.96 61.44 92.16 61.44 148.48v599.04c-5.12 20.48-20.48 35.84-40.96 35.84z m-389.12-204.8h261.12c30.72 0 61.44 10.24 92.16 25.6V296.96C471.04 261.12 460.8 230.4 435.2 204.8s-56.32-40.96-92.16-40.96H122.88v563.2z"
    //         fill="#007bff"
    //         p-id="7812"
    //       ></path>
    //       <path
    //         d="M512 931.84c-20.48 0-40.96-15.36-40.96-40.96V296.96c0-56.32 20.48-107.52 61.44-148.48s92.16-61.44 148.48-61.44h256c20.48 0 40.96 15.36 40.96 40.96V768c0 20.48-15.36 40.96-40.96 40.96h-296.96c-25.6 0-46.08 10.24-61.44 25.6s-25.6 40.96-25.6 61.44c0 20.48-20.48 35.84-40.96 35.84z m168.96-768c-35.84 0-66.56 15.36-92.16 40.96s-40.96 56.32-40.96 92.16v455.68c25.6-15.36 56.32-25.6 92.16-25.6h261.12v-563.2h-220.16z"
    //         fill="#007bff"
    //         p-id="7813"
    //       ></path>
    //     </svg>
    //     <span class="text-primary">{dataStore.questionSource?.subject}</span>
    //   </div>
    // );
  },
});

// 题列表
const BuildQuestionGroupList = defineComponent({
  setup() {
    const dataStore = useDataStore();

    return () => (
      <div class="flex-1 relative">
        {dataStore.allQuestionGroupList.length == 0 ? (
          <Empty />
        ) : (
          <div class="absolute w-full h-full overflow-y-auto hideScrollbar">
            <div class="space-y-3">
              {dataStore.allQuestionGroupList.map((e, i) => {
                return (
                  <div class="space-y-2">
                    <BuildTitle title={e.typeName} index={i} />
                    <div class="grid grid-cols-5 gap-2">
                      {e.list.map((e2) => {
                        return <BuildCardItem data={e2} />;
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );

    // 标题
    function BuildTitle(props: { title: string; index: number }) {
      return (
        <div class="text-gray-700 text-sm">
          {/* <span>{toChinese(props.index)}、</span> */}
          <span>{props.title}</span>
        </div>
      );
    }

    // 卡片
    function BuildCardItem(props: { data: Question.BaseQuestionModel }) {
      return (
        <div
          onClick={() => {
            dataStore.changeCurrentToIndex(props.data.index - 1);
          }}
          class={`border border-gray-400  overflow-hidden  flex items-center justify-center hover relative rounded ${(() => {
            // 所选
            if (dataStore.currentIndex == props.data.index - 1) {
              return "bg-primary border-primary text-white";
            }
            // 已完成
            if (props.data.isAnswerComplete == true) {
              return " bg-green-400 border-green-400 text-white";
            }
            return "bg-white";
          })()}`}
          style={{
            aspectRatio: 1 / 1,
          }}
        >
          {props.data.index}
          {props.data.isMark && (
            <div
              class=" absolute right-[0] bottom-[0]"
              style={{
                width: 0,
                height: 0,
                border: "7px solid",
                borderColor: "transparent red red transparent",
              }}
            />
          )}
        </div>
      );
    }
  },
});

// 答题卡说明
const BuildTips = defineComponent({
  setup() {
    return () => (
      <div class="flex justify-around ">
        <BuildItem className=" bg-primary " label="当前" />
        <BuildItem className=" bg-white" label="未完成" />
        <BuildItem className="bg-green-400 " label="已完成" />
        <BuildItem className=" bg-white " label="标记" isMark />
      </div>
    );

    function BuildItem(props: { className: string; label: string; isMark?: boolean }) {
      return (
        <div class="flex flex-col items-center space-y-1">
          <div class={` w-3 h-3 rounded border border-gray-400 relative ${props.className}`}>
            {props.isMark && (
              <div
                class="w-full h-full"
                style={{
                  backgroundImage:
                    "linear-gradient(-45deg, orangered 50%, rgba(255, 255, 255, 0) 50%)",
                }}
              />
            )}
          </div>
          <div class="text-xs">{props.label}</div>
        </div>
      );
    }
  },
});
