import { defineComponent, ref } from "vue";
import { <PERSON><PERSON>, Modal, Select, Option } from "@arco-design/web-vue";

export default defineComponent({
  setup() {
    const selectedJobIds = ref([]);
    const jobCategories = ref([]);
    const jobOptionsList = ref([]);

    const handleChange = (value: number, index: number) => {
      selectedJobIds.value[index] = value;

      if (jobCategories.value[index]?.name) {
        resetAfterSelection(index);
      }
      setTimeout(() => {}, 1000);
      getJobList(index + 1);
    };

    const resetAfterSelection = (categoryIndex: number) => {
      for (let index = categoryIndex + 1; index < jobCategories.value.length; index++) {
        selectedJobIds.value[index] = undefined;
        jobOptionsList.value[index] = [];
      }
    };

    // 选岗选项列表
    const getJobList = async (categoryIndex: any) => {
      const data = {
        a: [{ name: "a1" }, { name: "a2" }],
        b: [{ name: "b1" }, { name: "b2" }],
      };

      console.log(data, selectedJobIds.value[0]);
      jobOptionsList.value[categoryIndex] = data[selectedJobIds.value[0]];
    };

    setTimeout(() => {
      jobCategories.value = [{ name: "请选择a" }, { name: "请选择bb" }];
      jobOptionsList.value = [[{ name: "a" }, { name: "b" }]];
    }, 100);

    return () => (
      <div class="mt32">
        {jobCategories.value.map((jobCategory, index) => (
          <Select
            allowClear={true}
            model-value={selectedJobIds.value[index]}
            style={{ width: "360px" }}
            class="select-list-item"
            size={"large"}
            placeholder={`请选择${jobCategory.name}`}
            onChange={(val: any) => handleChange(val, index)}
          >
            {jobOptionsList.value[index]?.map((jobOption, sencondIndex) => (
              <Option key={sencondIndex}>{jobOption.name}</Option>
            ))}
          </Select>
        ))}
      </div>
    );
  },
});
