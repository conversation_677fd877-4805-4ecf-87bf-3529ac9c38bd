import Cookies from "js-cookie";

export function setUserMobileCookie(mobile: string): void {
  Cookies.set("userMobile", mobile, { expires: 1 });
}

export function getUserMobileCookie(): string {
  return Cookies.get("userMobile") || "";
}

export function setUserTokenCookie(token: string): void {
  Cookies.set("web_user_token", token, { expires: 1 });
}

export function getUserTokenCookie(): string {
  return Cookies.get("web_user_token") || "";
}

export function setVerified(verified: string): void {
  Cookies.set("verified", verified);
}

export function getVerified(): string {
  return Cookies.get("verified") || "";
}

export function setEndTime(endTime: any): void {
  Cookies.set("endTime", endTime);
}

export function getEndTime(): string {
  return Cookies.get("endTime") || "";
}

export function setType(type: any): void {
  Cookies.set("type", type);
}

export function getType(): string {
  return Cookies.get("type") || "";
}

export function setProvince(province: any): void {
  Cookies.set("province", province);
}

export function getProvince(): string {
  return Cookies.get("province") || "";
}

export function setTitle(title: any): void {
  Cookies.set("provinceTitle", title);
}

export function getTitle(): string {
  return Cookies.get("provinceTitle") || "";
}

export function setExam(exam: any): void {
  Cookies.set("exam", exam);
}

export function getExam(): string {
  return Cookies.get("exam") || "";
}

export function setUserPositionCookie(position: string): void {
  Cookies.set("UserPosition", position, { expires: 1 });
}

export function getUserPositionCookie(): string {
  return Cookies.get("UserPosition") || "";
}
