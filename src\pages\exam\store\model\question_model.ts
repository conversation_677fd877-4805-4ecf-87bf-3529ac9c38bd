import { Computer_getpaper_api } from "@/common/apis/apis";
import { isEmpty } from "lodash";

export namespace Question {
  // 选项类型
  export interface QuestionOptionItemModel {
    key: string;
    content: string;
  }

  interface IAnswerMethod {
    // 答题
    answer(value: string[]): void;
    // 获取选项
    getOptions(question: Computer_getpaper_api.QuestionsItemModel): QuestionOptionItemModel[];
    // 获取答案
    getAnswerList(question: Computer_getpaper_api.QuestionsItemModel): string[];
    //改变标记状态
    changeMarkState(): void;
  }

  // 接收到的参数
  export interface IProps {
    cost_time: number;
    question: Computer_getpaper_api.QuestionsItemModel;
    index: number;
    score: number;
    materialList: Computer_getpaper_api.QuestionsItemMaterialModel[];
  }

  // 基础
  export class BaseQuestionModel implements IAnswerMethod {
    constructor(props: IProps) {
      this.index = props.index;
      this.id = props.question.id;
      this.questionType = props.question.qt;
      this.group_id = props.question.group_id;
      this.type = props.question.t;
      this.score = props.score;
      this.title = props.question.q;
      this.options = this.getOptions(props.question);
      this.answerList = this.getAnswerList(props.question);
      this.materialList = props.materialList;
      this.cost_time = props.cost_time;
    }

    // 获取答案
    getAnswerList(question: Computer_getpaper_api.QuestionsItemModel): string[] {
      return question?.oa?.a ?? [];
    }

    // 获取选项
    getOptions(question: Computer_getpaper_api.QuestionsItemModel): QuestionOptionItemModel[] {
      return (
        question.oa?.opt.map<QuestionOptionItemModel>((e) => {
          return {
            key: e.k,
            content: e.c,
          };
        }) ?? []
      );
    }

    // 题id
    id: number;

    // 类型
    type: Computer_getpaper_api.QuestionsItemModel["t"];

    // 下标
    index: number;

    // 类型
    questionType: Computer_getpaper_api.QuestionsItemModel["qt"];

    // 分数
    score: number;

    // 标题
    title: string;

    // 答案列表
    answerList: string[] = [];

    // 选项
    options: QuestionOptionItemModel[] = [];

    // 答题输入的答案列表
    respondList: string[] = [];

    // 材料列表
    materialList: Computer_getpaper_api.QuestionsItemMaterialModel[];

    //是否标记
    isMark: boolean = false;

    // 分组ID
    group_id: string = "";

    // 用时
    cost_time: number;

    // 主观题特有字段
    content: string = "";

    // 是否答题完成
    get isAnswerComplete(): boolean {
      return !isEmpty(this.respondList);
    }

    // 是否答对
    get isAnserRight(): boolean {
      // 判断所答和选项数量是否相等
      if (this.answerList.length != this.respondList.length) {
        return false;
      }
      // 匹配每个输入的内容，是否和答案匹配
      else if (this.answerList.every((e) => this.respondList.some((e2) => e == e2))) {
        return true;
      } else {
        return false;
      }
    }

    // 答题
    answer(value: string[]): void {
      console.log(value);
      this.respondList = value;
    }

    changeMarkState(): void {
      this.isMark = !this.isMark;
    }
  }

  // 单选题
  export class SingleChoiceQuestionModel extends BaseQuestionModel {
    constructor(props: IProps) {
      super(props);
    }
    answer(value: string[]): void {
      this.respondList = value;
    }
  }

  // 多选题
  export class MultipleChoiceQuestionModel extends BaseQuestionModel {
    constructor(props: IProps) {
      super(props);
    }
    answer(value: string[]): void {
      this.respondList = value;
    }
  }

  // 判断题
  export class CheckQuestionModel extends BaseQuestionModel {
    constructor(props: IProps) {
      super(props);
    }

    answer(value: string[]): void {
      this.respondList = value;
    }
  }

  // 自己写的题
  export class WritingQuestionModel extends BaseQuestionModel {
    constructor(props: IProps) {
      super(props);
      this.content = "";
      // this.respondList = new Array(props.question.oa?.length ?? 0).fill("");
    }

    // getAnswerList(_: Computer_getpaper_api.QuestionsItemModel): string[] {
    //   return [];
    // }

    // getOptions(question: Computer_getpaper_api.QuestionsItemModel): QuestionOptionItemModel[] {
    //   try {
    //     return (
    //       question.oa?.map((e) => ({
    //         key: "",
    //         content: e,
    //       })) ?? []
    //     );
    //   } catch (e) {
    //     return [];
    //   }
    // }

    // 是否答对，当选项列表都输入了内容后，直接算答对
    // get isAnserRight(): boolean {
    //   return this.respondList.every((e) => !isEmpty(e));
    // }

    // // 选项框，只要有值，就表示完成
    // get isAnswerComplete(): boolean {
    //   return this.respondList.filter((e) => !isEmpty(e)).length > 0;
    // }

    // answer(_: string[]): void {
    //   console.log("分析题答题");
    // }
  }
}
