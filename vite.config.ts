import vueJsx from "@vitejs/plugin-vue-jsx";
import { defineConfig, loadEnv } from "vite";
// path 用于配置路径别名
import { resolve } from "path";
import legacy from "@vitejs/plugin-legacy";

const _resolve = (src: string): any => resolve(__dirname, src);

export default defineConfig((config) => {
  const env = loadEnv(config.mode, process.cwd());
  console.log(env["VITE_SERVICE_URL"]);
  return {
    plugins: [
      // legacy({
      //   targets: ["IE 8"],
      //   additionalLegacyPolyfills: ["regenerator-runtime/runtime"],
      //   modernPolyfills: true,
      // }),
      vueJsx(),
    ],
    base: env.VITE_BASE_URL,
    // 作为静态资源服务的文件夹。
    publicDir: "public",
    resolve: {
      alias: {
        "@": _resolve("src"),
        "@assets": _resolve("src/assets"),
        "@common": _resolve("src/common"),
        "@apis": _resolve("src/common/apis"),
        "@router": _resolve("src/common/router"),
        "@components": _resolve("src/common/components"),
        "@store": _resolve("src/common/store"),
        "@pages": _resolve("src/pages"),
      },
    },
    build: {
      target: "es2015", // 设置构建的目标版本
      outDir: "dist/simexam/jqxd",
      sourcemap: false, // 构建后是否生成 source map 文件
      emptyOutDir: true, // 构建时清空该目录
      chunkSizeWarningLimit: 1000, // 提高限制值
      // 移除了之前注释掉的部分，因为代码分割通常在现代框架中自动处理
      // 如果需要手动控制代码分割，应该在业务代码中使用动态导入来实现
    },
    css: {
      preprocessorOptions: {
        less: {
          // 覆盖组件库的颜色
          modifyVars: {
            "arcoblue-6": "#cccccc",
            "border-radius-small": "5px",
          },
          javascriptEnabled: true,
        },
      },
    },

    // 网络代理
    server: {
      proxy: {
        "/api": {
          target: env["VITE_SERVICE_URL"],
          changeOrigin: true,
          secure: false,
          rewrite: (path) => {
            return path.replace(/^\/api/, "");
          },
        },
      },
    },
  };
});
