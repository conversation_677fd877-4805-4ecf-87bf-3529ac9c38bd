import { useDataStore } from "@/pages/exam/store/dataStore";
import { Radio } from "@arco-design/web-vue";
import { isEmpty } from "lodash";
import { computed, defineComponent, watch } from "vue";
import RiceTextEditor from "@components/rice_text_editor";
import { Tabs } from "@arco-design/web-vue";
export default defineComponent({
  setup() {
    const dataStore = useDataStore();

    const currentQuestion = computed(() => {
      return dataStore.currentQuestion;
    });

    watch(
      () => currentQuestion.value!.content,
      (text) => {
        const current = dataStore.currentQuestion;
        if (text?.length > 0) {
          if (!current?.respondList?.length) {
            current.respondList[0] = "t";
          }
        }
      }
    );

    return () => (
      <div>
        {/* 
        <div class="text-primary mb-1" style="font-size:15px;color:rgb(156, 163, 175)">
          *主观题请选择是否掌握自评得分。您可以在下方框内模拟作答，但不计入得分。
        </div> */}
        <div class="text-primary mb-2 mt-1" style="color:red;opacity:0.75">
          温馨提醒：公文作答仅用于考生模拟机考系统，不做批改和赋分。提交试卷后公文写作作答记录不会保存，请考生自行复制/截图留存。
        </div>
        <div class="space-y-3 h-full flex flex-col">
          <div class="flex-1 relative">
            <div class=" w-full h-full ">
              {/* <Tabs type="card-gutter">
                {[1].map((_, i) => {
                  return (
                    <Tabs.TabPane key={i} title={`文本作答区`}>
                      <div class="space-y-5 p-3 ">
               
                      </div>
                    </Tabs.TabPane>
                  );
                })}
              </Tabs> */}

              <RiceTextEditor v-model={currentQuestion.value!.content} />
            </div>
          </div>
        </div>
        <Radio.Group
          onChange={(e) => {
            currentQuestion.value?.answer([e as string]);
          }}
          modelValue={
            isEmpty(currentQuestion.value?.respondList) ? "" : currentQuestion.value?.respondList[0]
          }
          direction="vertical"
          size="large"
          class=" space-y-3 mb-5 mt-3"
        >
          {currentQuestion.value?.options?.map((e, i) => (
            <Radio key={e.content} value={e.key}>
              {{
                radio: (radioE: { checked: boolean }) => {
                  return (
                    <div class="flex items-center space-x-3">
                      <div
                        class={`w-5 h-5  rounded-full border-gray-400 border flex items-center justify-center text-xl ${
                          radioE.checked && " border-primary text-primary"
                        }`}
                      >
                        {radioE.checked && <div class=" w-3 h-3 rounded-full bg-primary"></div>}
                      </div>
                      <div
                        class="user-select-none"
                        style={{
                          fontSize: dataStore.fontSize + "px",
                        }}
                      >
                        {/* <span class=" font-bold mr-2">{String.fromCharCode(i + 65)}.</span> */}
                        {e.content}
                      </div>
                    </div>
                  );
                },
              }}
            </Radio>
          ))}
        </Radio.Group>
      </div>
    );
  },
});
