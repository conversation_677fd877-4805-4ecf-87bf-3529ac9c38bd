import { defineComponent, ref, watch } from "vue";
import { RouterView, useRoute } from "vue-router";
import { Check_Token, Login_login_api } from "./common/apis/apis";
import {
  getProvince,
  setExam,
  setProvince,
  setTitle,
  setUserTokenCookie,
  setVerified,
} from "@common/cookies/user.ts";
import { updateToken } from "@common/utils/axios_request";
import { useExamInfoStore } from "@common/store/examInfoStore.tsx";
import routerInstance from "@common/router";

export default defineComponent({
  setup() {
    const route = useRoute();
    const examInfoStore = useExamInfoStore();
    const isRequest = ref(false);
    let province = "";
    let exam = "";
    watch(route, () => {
      // 在路由变更时输出params
      province = route.query.province as string;
      exam = route.query.exam as string;
      if (province) {
        setProvince(province);
      }
      if (exam) {
        setExam(exam);
      }
      checkToken();
    });

    async function checkToken() {
      const province = getProvince();
      await Check_Token.api({
        data: {
          province: province,
        },
      }).then(async (res: Check_Token.Model) => {
        if (res) {
          if (res.data.title) {
            setTitle(res.data.title);
          }
          if (res.data.user.verified) {
            const response = await Login_login_api.api({
              data: {
                examkey: route.params.examKey as string,
                idcard: "1234",
                mobile: res.data.user.mobile,
                scheme: "auto",
                code: "",
              },
            });

            setUserTokenCookie(response.token);
            setVerified(String(1));
            updateToken(response.token);
            examInfoStore.setInfo(response);
          } else {
            setUserTokenCookie("");
            updateToken("");
            setVerified(String(0));
            routerInstance.replace({ name: "index" });
            // router.replace({name: "rule"});
          }
          isRequest.value = true;
        }
      });
    }
    console.log(route.fullPath);

    return () => <div>{isRequest.value ? <RouterView key={route.fullPath} /> : null}</div>;
  },
  mounted() {
    // const route = useRoute() as RouteLocationNormalizedLoaded;
    // setTimeout(() => {
    //   Check_Examkey_Valid.api({
    //     data: { examkey: route.params.examKey as string },
    //   }).then((res) => {
    //     if (res.is_valid !== 1 && route.name !== "empty") {
    //       router.replace({ name: "empty" });
    //     }
    //   });
    // });
  },
});
