import routerInstance from "@/common/router";
import image3 from "@assets/images/image3.png";
import { defineComponent, ref } from "vue";
import Home from "./components/home";
import User_info from "./components/user_info";
import "./index.scss";
import { Login_login_api } from "@/common/apis/apis";
import { useExamInfoStore } from "@/common/store/examInfoStore";
import {
  getVerified,
  setUserMobileCookie,
  setUserTokenCookie,
  setVerified,
} from "@/common/cookies/user";
import { useRoute } from "vue-router";
import { updateToken } from "@common/utils/axios_request";
// import exam from "../exam";
export default defineComponent({
  setup() {
    const examInfoStore = useExamInfoStore();
    const route = useRoute();
    const current = getVerified() ? ref(Number(getVerified())) : ref(1);
    const components = [
      <Home
        onOnNextEvent={async (inputForm: any) => {
          try {
            const respose = await Login_login_api.api({
              data: {
                examkey: route.params.examKey as string,
                idcard: "1234",
                mobile: inputForm.phone,
                code: inputForm.code,
                scheme: "manual",
              },
            });
            setUserTokenCookie(respose.token);
            updateToken(respose.token);
            setUserMobileCookie(inputForm.phone);
            setVerified(String(1));
            examInfoStore.setInfo(respose);
            current.value += 1;
          } catch (error) {
            console.log("请求失败");
          }
        }}
      />,
      <User_info
        current={current.value}
        onBackEvent={() => {
          current.value -= 1;
          setUserTokenCookie("");
          updateToken("");
          setVerified(String(0));
        }}
        onNextEvent={() => {
          console.log("进来了咩哦");
          routerInstance.push({ name: "rule" });
        }}
      />,
    ];

    return () => (
      <div
        class="w-screen h-screen relative py-96"
        style={{
          // backgroundImage: `url(${image3})`,
          backgroundSize: "100% 100%",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div class=" absolute right-4 -top-4 text-red-400" style="font-size:120px"></div>
        {components[current.value]}
      </div>
    );
  },
});
