import { useDataStore } from "@/pages/exam/store/dataStore";
import { computed, defineComponent } from "vue";

export default defineComponent({
  setup() {
    const dataStore = useDataStore();
    const isMark = computed(() => dataStore.currentQuestion?.isMark);
    return () => (
      <div
        onClick={() => {
          dataStore.currentQuestion?.changeMarkState();
        }}
        class={"flex items-center space-x-2 hover "}
      >
        <div
          class={`  rounded-full w-6 h-6 flex items-center justify-center  overflow-hidden   ${
            isMark.value ? "bg-red-400" : "bg-gray-300"
          }`}
        >
          <svg
            //@ts-ignore
            t="1721702805273"
            class=" w-4"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="5164"
            width="64"
            height="64"
          >
            <path
              d="M915.072 268.416a264.384 264.384 0 0 0-159.488-159.488c-12.608-4.544-27.52-9.92-40.576-11.904a91.328 91.328 0 0 0-46.144 5.056c-34.56 12.416-50.048 40.256-67.072 70.912l-33.024 59.264c-12.224 21.888-20.352 36.416-27.52 46.72-6.912 10.112-10.816 12.928-13.056 14.08-5.248 2.816-14.784 2.56-23.232 2.304l-3.456-0.128c-18.496-0.384-43.584-2.56-79.232-5.76-55.808-4.928-111.104 4.992-162.304 36.928-8.768 5.504-19.776 12.352-27.968 20.032a89.344 89.344 0 0 0-22.016 33.088c-5.568 13.696-7.808 26.24-7.232 39.68 0.512 11.52 3.52 24.64 6.016 35.648 16.128 71.04 53.376 139.264 102.848 197.184l-203.136 203.136a42.688 42.688 0 1 0 60.352 60.352l203.136-203.136c57.92 49.472 126.208 86.72 197.184 102.848 11.008 2.56 24.192 5.504 35.648 6.016 13.44 0.64 25.984-1.664 39.68-7.232 13.504-5.44 24-12.288 33.088-22.016 7.68-8.192 14.528-19.2 20.032-27.904 32.448-51.904 41.792-107.712 36.352-163.904-3.328-34.944-5.696-59.456-6.208-77.44-0.512-19.2 1.472-25.088 2.112-26.24 1.152-2.368 4.032-6.208 14.08-13.184a633.6 633.6 0 0 1 46.528-27.392l60.544-33.728c30.72-17.024 58.496-32.512 70.912-67.072 5.824-16.128 7.36-30.72 5.056-46.144a226.624 226.624 0 0 0-11.904-40.576z"
              fill="#ffffff"
              p-id="5165"
            ></path>
          </svg>
        </div>
        <div class=" text-base text-gray-400">
          {isMark.value ? "取消标记" : "标记本题"}
        </div>
      </div>
    );
  },
});
