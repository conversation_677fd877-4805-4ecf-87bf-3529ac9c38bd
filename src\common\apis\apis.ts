import axios_request from "@common/utils/axios_request/index";
import { ServicePostOptions } from "../utils/axios_request/types";

export namespace common {
  // 1=单项选择题 2=多项选择题 3=判断题 4=简答题 8=综合题 9=案例分析题 10=计算题 11=计算问答题 12=综合案例分析题 13=法律文书题 14=主观题 15=客观题 16=不定项选择题
  export type QuestionType = 1 | 2 | 3 | 4 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16;
}

// 考试试卷信息
export namespace Computer_getpaperinfo_api {
  export interface Model {
    // 试卷id
    id: string;
    cs_id: string;
    c_id: string;
    // 试卷编号
    qv_no: string;
    // 考试科目名称
    name: string;
    indexid: string;
    status: string;
    // 人数
    participants: string;
    // 试题类型说明
    typer: {
      id: string;
      title: string;
      describe: string;
    }[];
    // 考试时间
    test_time: string;
    end_time: string;
    addtime?: any;
    updatetime?: any;
  }

  export interface ApiParam {
    examkey: string;
    type: any;
  }

  export const api = (param: ServicePostOptions<ApiParam>) =>
    axios_request.post<Model>("/get_group_list", param);
}

// 获取试题
export namespace Computer_getpaper_api {
  export interface Model {
    // 考试科目名称
    subject: string;
    // 标题
    exam: string;
    // 准考证号
    prove: string;
    // 试卷编号
    no: string;
    // 试卷id
    id: string;
    // 题集 (单选、多选、案例分析.....)
    questions: Record<
      common.QuestionType,
      {
        type: common.QuestionType;
        id: string;
        title: string;
        list: QuestionsItemModel[];
      }
    >;
    // 材料
    qd?: Record<string, QuestionsItemMaterialModel[]>;
    // 每题对应的分数
    scores: Record<string, string>;
    // 试卷编号
    qv_no: string;
    name: string;
    sex: string;
    // 作答记录
    answer: object;
    used_time: any;
  }

  // 题
  export interface QuestionsItemModel {
    id: number;
    s: string;
    t: number;
    qt: common.QuestionType;
    qd: number;
    q: string;
    oa?: {
      opt: {
        k: string;
        c: string;
      }[];
      a: string[];
    } & string[];
    ans: string;
    pk: string;
    pn: string;
    p: string;
    w?: string;
    group_id: string;
  }

  // 材料
  export interface QuestionsItemMaterialModel {
    label: string;
    text: string;
    status?: string;
    img?: any[];
    img_arr?: any[];
  }
  export interface ApiParam {
    examkey: string;
    type: any;
  }

  export const api = (param: ServicePostOptions<ApiParam>) =>
    axios_request.post<Model>("/get_question", param);
}

export namespace Login_sms_api {
  export interface Model {
    data: any[];
    msg: string;
    status: number;
  }
  export const api = (param: Object) => {
    return axios_request.post<Model>("/send_sms", param);
  };
}

export namespace Login_login_api {
  export interface Model {
    token: string;
    adcard: number;
    exam_name: string;
    idcard: number;
    name: string;
    no: string;
    subject_name: string;
    test_time: number;
    time: string;
  }

  export interface ApiParam {
    examkey: string;
    idcard: string;
    mobile: string;
    code: string;
    scheme: string;
  }
  export const api = (param: ServicePostOptions<ApiParam>) => {
    return axios_request.post<Model>("/login", param);
  };
}

export namespace Computer_Submit_api {
  interface share {
    shareid: any;
  }
  export interface Model {
    data: share;
  }

  export interface ApiParam {
    answer: object;
    brand: string;
    end_time: number;
    start_time: number;
    paper_id: string;
    post_type: string;
    purpose: string;
    type: string;
    used_time: number;
    rec_job_id: string;
  }
  export const api = (param: ServicePostOptions<ApiParam>) => {
    return axios_request.post<Model>("/certain", param);
  };
}

export namespace Check_Examkey_Valid {
  export interface Model {
    is_valid: number; // 0 无效 1 有效
  }

  export interface ApiParam {
    examkey: string;
  }
  export const api = (param: ServicePostOptions<ApiParam>) => {
    return axios_request.post<Model>("/check_examkey_valid", param);
  };
}

export namespace Check_Token {
  interface Obj {
    verified: boolean;
    mobile: string;
  }

  interface User {
    title: string;
    user: Obj;
    mobile: any;
    verified: string;
  }

  export interface Model {
    data: User;
  }

  export interface ApiParam {
    province: string;
  }
  export const api = (param: ServicePostOptions<ApiParam>) => {
    return axios_request.post<Model>("/get_user_info", param);
  };
}

export namespace Job_api {
  export interface Model {
    data: any[];
  }

  export interface ApiParam {
    examkey: string;
  }
  export const api = (param: ServicePostOptions<ApiParam>) => {
    return axios_request.post<Model>("/job", param);
  };
}

export namespace Job_list_api {
  export interface Model {
    data: any[];
  }

  export interface ApiParam {
    examkey: string;
    field: string;
  }
  export const api = (param: ServicePostOptions<ApiParam>) => {
    return axios_request.post<Model>("/get_job", param);
  };
}

export namespace paper_info_api {
  export interface Model {
    data: any[];
  }

  export interface ApiParam {
    examkey: string;
  }
  export const api = (param: ServicePostOptions<ApiParam>) => {
    return axios_request.post<Model>("/get_paper_info", param);
  };
}
