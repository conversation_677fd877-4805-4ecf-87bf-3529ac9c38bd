{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npx vite build", "build-tsc": "vue-tsc -b && vite build", "preview": "vite preview", "build:skb": "export VITE_BRAND=skb && npx vite build", "build:jbcgk": "set VITE_BRAND=jbcgk && npx vite build", "build:gpjs": "export VITE_BRAND=gpjs && npx vite build", "build:jbczsb": "export VITE_BRAND=jbczsb && npx vite build", "build:xuandiao": "export VITE_BRAND=xuandiao && npx vite build", "git-push--skb": "node ./git-commit.cjs build:skb", "git-push--jbcgk": "node ./git-commit.cjs build:jbcgk", "git-push--gpjs": "node ./git-commit.cjs build:gpjs", "git-push--jbczsb": "node ./git-commit.cjs build:jbczsb", "git-push--xuandiao": "node ./git-commit.cjs build:xuandiao"}, "dependencies": {"@tinymce/tinymce-vue": "^5", "@vueuse/core": "^10.1.2", "@vueuse/router": "^10.1.2", "axios": "^1.3.4", "element-plus": "^2.8.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "pinia": "^2.0.33", "validate": "^5.2.0", "vue": "^3.4.29", "vue-loader": "^17.3.1", "vue-loader-v16": "^16.0.0-beta.5.4", "vue-request": "^2.0.0-rc.4", "vue-router": "^4.1.6"}, "devDependencies": {"@arco-design/web-vue": "^2.44.2", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.192", "@types/node": "^18.15.3", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "autoprefixer": "^10.4.14", "less": "^4.1.3", "path": "^0.12.7", "postcss": "^8.4.21", "sass": "^1.60.0", "sass-loader": "^7.3.1", "shelljs": "^0.8.5", "tailwindcss": "^3.2.7", "typescript": "^5.2.2", "vite": "^5.3.1", "vite-plugin-style-import": "^2.0.0", "vue-tsc": "^2.0.21"}}